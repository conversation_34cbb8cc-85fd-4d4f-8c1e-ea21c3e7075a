# 题目导入测试样例

以下是用于测试题库导入功能的标准格式样例：

## 单选题示例

1、[单选题]计算机的CPU主要功能是什么？
A、存储数据
B、处理数据和控制程序执行
C、输入数据
D、输出数据
答案：B
解析：CPU（中央处理器）是计算机的核心部件，主要负责处理数据和控制程序的执行。

2、[单选题]以下哪个不是操作系统？
A、Windows
B、Linux
C、Office
D、macOS
答案：C
解析：Office是办公软件套件，不是操作系统。Windows、Linux和macOS都是操作系统。

## 多选题示例

3、[多选题]以下哪些是计算机的输入设备？
A、键盘
B、鼠标
C、显示器
D、扫描仪
E、打印机
答案：A,B,D
解析：键盘、鼠标和扫描仪都是输入设备，显示器是输出设备，打印机也是输出设备。

4、[多选题]网络协议中，以下哪些属于应用层协议？
A、HTTP
B、FTP
C、TCP
D、SMTP
E、IP
答案：A,B,D
解析：HTTP、FTP、SMTP都是应用层协议，TCP是传输层协议，IP是网络层协议。

## 判断题示例

5、[判断题]CPU的主频越高，计算机的性能就一定越好。
答案：错误
解析：CPU性能不仅取决于主频，还与架构、缓存、核心数等多个因素有关。

6、[判断题]计算机病毒可以通过网络传播。
答案：正确
解析：计算机病毒可以通过多种途径传播，包括网络、移动存储设备、电子邮件等。

## 格式说明

### 题目编号格式
- 使用"数字、"开头，如"1、"、"2、"等

### 题型标识格式
- 单选题：[单选题]
- 多选题：[多选题]  
- 判断题：[判断题]

### 选项格式
- 使用大写字母加顿号，如"A、"、"B、"等
- 每个选项独占一行

### 答案格式
- 单选题：答案：A（单个字母）
- 多选题：答案：A,B,D（多个字母用逗号分隔）
- 判断题：答案：正确 或 答案：错误

### 解析格式
- 解析：（解析内容）

## 注意事项

1. 每道题目之间建议空一行，便于区分
2. 题目内容可以包含数学公式、特殊符号等
3. 选项内容支持多行文本
4. 解析部分为可选项，可以省略
5. 文档编码建议使用UTF-8，避免中文乱码

## 复杂题目示例

7、[单选题]在关系数据库中，以下关于主键的描述哪个是正确的？
A、主键可以为空值
B、一个表可以有多个主键
C、主键值必须唯一且不能为空
D、主键只能是数字类型
答案：C
解析：主键是关系数据库表中用于唯一标识每一行记录的字段或字段组合。主键的特点是：值必须唯一、不能为空、一个表只能有一个主键。主键可以是任何数据类型，不限于数字。

8、[多选题]以下关于面向对象编程的特征，哪些是正确的？
A、封装性：将数据和操作数据的方法结合在一起
B、继承性：子类可以继承父类的属性和方法
C、多态性：同一个接口可以有不同的实现
D、抽象性：将复杂的实现细节隐藏起来
答案：A,B,C,D
解析：面向对象编程的四大特征包括：
1. 封装性：将数据和方法封装在类中
2. 继承性：子类继承父类的特性
3. 多态性：同一接口的不同实现
4. 抽象性：隐藏实现细节，突出本质特征

9、[判断题]在Java中，String类型的变量是可变的。
答案：错误
解析：在Java中，String对象是不可变的（immutable）。一旦创建了String对象，就不能修改它的值。如果需要修改字符串，会创建新的String对象。如果需要可变的字符串，应该使用StringBuilder或StringBuffer类。

## 导入测试步骤

1. 将以上内容复制到Word文档中
2. 保存为.docx格式
3. 在题库管理页面点击"文档导入"
4. 上传Word文档
5. 在富文本编辑器中查看和编辑内容
6. 查看右侧实时预览效果
7. 确认无误后点击导入

## 预期结果

- 应该成功解析出9道题目
- 包含3道单选题、3道多选题、3道判断题
- 每道题目都应该有正确的题型、选项、答案和解析
- 预览区域应该正确显示题目格式
