import request from '@/utils/request'

// 查询题库列表
export function listQuestionBank(query) {
  return request({
    url: '/biz/questionBank/list',
    method: 'get',
    params: query
  })
}

// 查询题库详细
export function getQuestionBank(bankId) {
  return request({
    url: '/biz/questionBank/' + bankId,
    method: 'get'
  })
}

// 新增题库
export function addQuestionBank(data) {
  return request({
    url: '/biz/questionBank',
    method: 'post',
    data: data
  })
}

// 修改题库
export function updateQuestionBank(data) {
  return request({
    url: '/biz/questionBank',
    method: 'put',
    data: data
  })
}

// 删除题库
export function delQuestionBank(bankId) {
  return request({
    url: '/biz/questionBank/' + bankId,
    method: 'delete'
  })
}

// 调试文档内容
export function debugDocumentContent(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/biz/questionBank/debugDocumentContent',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量导入题目
export function batchImportQuestions(data) {
  return request({
    url: '/biz/questionBank/batchImportQuestions',
    method: 'post',
    data: data
  })
}

// 上传文档并解析 - 新版本支持富文本编辑
export function uploadAndParse(file, bankId) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('bankId', bankId)
  return request({
    url: '/biz/questionBank/uploadAndParse',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 保存编辑后的内容
export function saveEditedContent(data) {
  return request({
    url: '/biz/questionBank/saveEditedContent',
    method: 'post',
    data: data
  })
}

// 实时预览编辑内容
export function previewContent(data) {
  return request({
    url: '/biz/questionBank/previewContent',
    method: 'post',
    data: data
  })
}

// 从编辑器内容导入题目
export function importFromEditor(data) {
  return request({
    url: '/biz/questionBank/importFromEditor',
    method: 'post',
    data: data
  })
}
