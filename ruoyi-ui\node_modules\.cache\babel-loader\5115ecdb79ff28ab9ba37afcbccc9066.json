{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_QuestionCard", "_interopRequireDefault", "require", "_QuestionForm", "_BatchImport", "_RichTextImport", "_question", "_questionBank", "_methods", "name", "components", "QuestionCard", "QuestionForm", "BatchImport", "RichTextImport", "data", "_ref", "bankId", "bankName", "statistics", "total", "singleChoice", "multipleChoice", "judgment", "questionList", "queryParams", "pageNum", "pageSize", "questionType", "difficulty", "questionContent", "expandAll", "selectedQuestions", "_defineProperty2", "default", "reverse", "allowDuplicate", "process", "env", "VUE_APP_BASE_API", "Authorization", "$store", "getters", "token", "watch", "documentContent", "handler", "newVal", "isSettingFromBackend", "trim", "debounceParseDocument", "parsedQuestions", "parseErrors", "immediate", "importDrawerVisible", "_this", "$nextTick", "initRichEditor", "rich<PERSON><PERSON><PERSON>", "destroy", "editorInitialized", "created", "initPage", "debounce", "parseDocument", "uploadData", "uploadHeaders", "mounted", "loadCachedContent", "window", "addEventListener", "handleBeforeUnload", "<PERSON><PERSON><PERSON><PERSON>", "saveToCacheNow", "autoSaveTimer", "clearTimeout", "removeEventListener", "methods", "_this$$route$query", "$route", "query", "$message", "error", "goBack", "getQuestionList", "getStatistics", "$router", "back", "_this2", "params", "convertQueryParams", "listQuestion", "then", "response", "rows", "catch", "console", "convertedParams", "_objectSpread2", "typeMap", "difficultyMap", "Object", "keys", "for<PERSON>ach", "key", "undefined", "_this3", "getQuestionStatistics", "handleBatchImport", "batchImportVisible", "handleRichTextImport", "richTextImportVisible", "handleRichTextImportSuccess", "success", "handleAddQuestion", "type", "currentQuestionType", "currentQuestionData", "questionFormVisible", "toggleExpandAll", "expandedQuestions", "handleExportQuestions", "length", "warning", "info", "concat", "handleToggleSelectAll", "isAllSelected", "map", "q", "questionId", "handleBatchDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "deletePromises", "delQuestion", "Promise", "all", "allSelected", "_this5", "handleQuestionSelect", "selected", "includes", "push", "index", "indexOf", "splice", "handleToggleExpand", "handleEditQuestion", "question", "handleCopyQuestion", "copiedQuestion", "createTime", "updateTime", "createBy", "updateBy", "convertQuestionTypeToString", "handleDeleteQuestion", "_this6", "replace", "displayContent", "substring", "handleQuestionFormSuccess", "handleBatchImportSuccess", "handleDrawerClose", "done", "showDocumentImportDialog", "_this7", "isUploading", "isParsing", "uploadComponent", "$refs", "documentUpload", "clearFiles", "documentImportDialogVisible", "showRulesDialog", "activeRuleTab", "rulesDialogVisible", "copyExampleToEditor", "_this8", "htmlTemplate", "setData", "downloadExcelTemplate", "download", "downloadWordTemplate", "beforeUpload", "file", "isValidType", "endsWith", "isLt10M", "size", "handleUploadSuccess", "_this9", "code", "setTimeout", "questions", "collapsed", "allExpanded", "errors", "errorCount", "originalContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentHtmlContent", "lastSaveTime", "Date", "toLocaleString", "msg", "handleUploadError", "collapseAll", "_this0", "$set", "toggleQuestion", "updateAllExpandedState", "toggleAllQuestions", "_this1", "every", "allCollapsed", "confirmImport", "_this10", "importQuestions", "_this11", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "questionsToImport", "importData", "_t", "w", "_context", "n", "p", "_toConsumableArray2", "importOptions", "batchImportQuestions", "v", "Error", "clearCache", "a", "_this12", "CKEDITOR", "warn", "fallbackToTextarea", "<PERSON><PERSON><PERSON><PERSON>", "document", "getElementById", "innerHTML", "showFallbackEditor", "height", "toolbar", "items", "removeButtons", "language", "removePlugins", "resize_enabled", "extraPlugins", "<PERSON><PERSON><PERSON><PERSON>", "fontSize_sizes", "fontSize_defaultLabel", "colorButton_enableMore", "colorButton_colors", "filebrowserUploadUrl", "image_previewText", "baseHref", "pluginsLoaded", "instanceReady", "editor", "evt", "on", "dialog", "getName", "checkInterval", "setInterval", "urlField", "getContentElement", "getValue", "startsWith", "clearInterval", "selectPage", "e", "removeDialogTabs", "log", "fallback<PERSON><PERSON>r", "rawContent", "getData", "contentWithRelativeUrls", "convertUrlsToRelative", "preserveRichTextFormatting", "stripHtmlTagsKeepImages", "hasUnsavedChanges", "saveToCache", "contentToLoad", "_this13", "textarea", "createElement", "className", "placeholder", "value", "style", "cssText", "target", "append<PERSON><PERSON><PERSON>", "stripHtmlTags", "html", "div", "textContent", "innerText", "content", "func", "wait", "timeout", "executedFunction", "_len", "arguments", "args", "Array", "_key", "later", "apply", "<PERSON><PERSON><PERSON><PERSON>", "location", "origin", "urlRegex", "RegExp", "parseResult", "parseQuestionContent", "message", "lines", "split", "line", "filter", "slice", "currentQuestionLines", "questionNumber", "i", "isQuestionStart", "isQuestionStartLine", "isQuestionTypeStart", "questionText", "join", "parsedQuestion", "parseQuestionFromLines", "_parsedQuestion$quest", "test", "contentStartIndex", "typeMatch", "match", "typeText", "remainingContent", "inferQuestionType", "isOptionLine", "isAnswerLine", "isExplanationLine", "isDifficultyLine", "cleanLine", "finalQ<PERSON>ionContent", "removeQuestionNumber", "typeName", "getTypeDisplayName", "explanation", "options", "<PERSON><PERSON><PERSON><PERSON>", "optionResult", "parseOptionsFromLines", "parseQuestionMetaFromLines", "processImagePaths", "processedContent", "before", "src", "after", "fullSrc", "result", "_this14", "images", "imageIndex", "contentWithPlaceholders", "finalContent", "img", "startIndex", "isArray", "optionMatch", "optionKey", "toUpperCase", "optionContent", "label", "multipleOptionsMatch", "singleOptions", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "singleOption", "err", "f", "answerMatch", "parseAnswerValue", "explanationMatch", "difficultyMatch", "extractAnswerFromQuestionContent", "patterns", "_i3", "_patterns", "pattern", "matches", "lastMatch", "answer", "answerText", "trimmedAnswer", "splitByQuestionType", "sections", "typeRegex", "lastIndex", "currentType", "exec", "parseSectionQuestions", "section", "_this15", "convertQuestionType", "questionBlocks", "splitByQuestionNumber", "block", "parseQuestionBlock", "blocks", "numberRegex", "firstLine", "currentLineIndex", "numberMatch", "parseOptions", "nextIndex", "parseQuestionMeta", "currentIndex", "parseAnswer", "extractAnswerFromContent", "bracketPatterns", "_i4", "_bracketPatterns", "matchAll", "getQuestionTypeName", "getQuestionTypeColor", "colorMap", "cachedData", "localStorage", "getItem", "cache<PERSON>ey", "JSON", "parse", "_this16", "dataToSave", "timestamp", "now", "setItem", "stringify", "removeItem", "event", "returnValue", "manualSave", "handleCacheCommand", "command", "_this17", "exportDraft", "blob", "Blob", "url", "URL", "createObjectURL", "link", "href", "toISOString", "body", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "getFormattedQuestionContent", "htmlContent", "extractQuestionFromHtml", "cleanupQuestionContent", "plainContent", "plainText", "paragraphs", "_iterator2", "_step2", "paragraph", "paragraphText", "cleanParagraphText", "handleSearch", "resetSearch"], "sources": ["src/views/biz/questionBank/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"importDrawerVisible = true\"\n        >\n          批量导题\n        </el-button>\n        <el-button\n          type=\"warning\"\n          icon=\"el-icon-edit\"\n          @click=\"handleRichTextImport\"\n          style=\"margin-left: 10px;\"\n        >\n          文档导入\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <span class=\"orange\">最后保存的草稿时间：{{ lastSaveTime || '暂无' }}</span>\n            <span v-if=\"hasUnsavedChanges\" class=\"unsaved-indicator\">●</span>\n            <div class=\"fr\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                @click=\"manualSave\"\n                :disabled=\"!hasUnsavedChanges\"\n              >\n                <i class=\"el-icon-document\"></i> 保存草稿\n              </el-button>\n              <el-dropdown trigger=\"click\" @command=\"handleCacheCommand\">\n                <el-button size=\"mini\" type=\"info\">\n                  <i class=\"el-icon-more\"></i>\n                </el-button>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item command=\"clear\">清除缓存</el-dropdown-item>\n                  <el-dropdown-item command=\"export\">导出草稿</el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}.</font>\n                      <span class=\"question-type-tag\" v-if=\"question.typeName\">【{{ question.typeName }}】</span>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 题干内容 -->\n                    <div class=\"question-main-content\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n                    <!-- 答案显示 -->\n                    <div class=\"question-answer-section\">\n                      <span class=\"question-answer-label\">答案：</span>\n                      <span class=\"question-answer-value\">{{ question.correctAnswer }}</span>\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button>\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1.建议使用新版office或WPS软件编辑题目文件，仅支持上传.docx/.xlsx格式的文件<br>\n          2.Word导入支持全部题型，Excel导入不支持完形填空题、组合题<br>\n          3.Word导入支持导入图片/公式，Excel导入暂不支持<br>\n          4.题目数量过多、题目文件过大（如图片较多）等情况建议分批导入<br>\n          5.需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n\n    <!-- 富文本导入组件 -->\n    <rich-text-import\n      :visible.sync=\"richTextImportVisible\"\n      :bank-id=\"bankId\"\n      :bank-name=\"bankName\"\n      @success=\"handleRichTextImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport RichTextImport from './components/RichTextImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport,\n    RichTextImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      // 选择状态\n      selectedQuestions: [],\n      // 选择相关\n      selectedQuestions: [],\n      isAllSelected: false,\n      expandedQuestions: [],\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      currentImportMode: 'excel', // 当前导入模式\n      // 富文本导入\n      richTextImportVisible: false,\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '', // 存储富文本HTML内容用于预览\n      parsedQuestions: [],\n      parseErrors: [],\n      // 全部展开/收起状态\n      allExpanded: true,\n      // 标志位：是否正在从后端设置内容（避免触发前端重新解析）\n      isSettingFromBackend: false,\n      lastSaveTime: '',\n      // 缓存相关\n      cacheKey: 'questionBank_draft_content',\n      autoSaveTimer: null,\n      hasUnsavedChanges: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      // 规范对话框标签页\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时初始化编辑器\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n    this.loadCachedContent()\n\n    // 监听页面关闭事件，保存内容\n    window.addEventListener('beforeunload', this.handleBeforeUnload)\n  },\n\n  beforeDestroy() {\n    // 保存当前内容到缓存\n    this.saveToCacheNow()\n\n    // 清理定时器\n    if (this.autoSaveTimer) {\n      clearTimeout(this.autoSaveTimer)\n    }\n\n    // 移除事件监听器\n    window.removeEventListener('beforeunload', this.handleBeforeUnload)\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n        console.error('获取题目列表失败', error)\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n        console.error('获取统计数据失败', error)\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n    // 批量导入\n    handleBatchImport() {\n      this.batchImportVisible = true\n    },\n    // 富文本导入\n    handleRichTextImport() {\n      this.richTextImportVisible = true\n    },\n    // 富文本导入成功回调\n    handleRichTextImportSuccess() {\n      this.richTextImportVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n      this.$message.success('题目导入成功')\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 这里应该调用批量删除API\n        // 暂时使用单个删除的方式\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.allSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          console.error('批量删除失败', error)\n          this.$message.error('批量删除失败')\n        })\n      })\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 批量删除API调用\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          console.error('批量删除失败', error)\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        this.expandedQuestions.splice(index, 1)\n      } else {\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n          console.error('删除题目失败', error)\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》 答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会 答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。 答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n    // 下载Excel模板\n    downloadExcelTemplate() {\n      this.download('biz/questionBank/downloadExcelTemplate', {}, `题目导入Excel模板.xlsx`)\n    },\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n          console.error('❌ 解析失败:', {\n            errors: response.errors,\n            response: response\n          })\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n          this.lastSaveTime = new Date().toLocaleString()\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n        console.error('上传失败响应:', response)\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n      console.error('上传失败:', error, file?.name)\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n    // 全部收起\n    collapseAll() {\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', true)\n      })\n    },\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n\n      // 同步全部展开/收起状态\n      this.updateAllExpandedState()\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n    },\n\n    // 更新全部展开/收起状态\n    updateAllExpandedState() {\n      if (this.parsedQuestions.length === 0) {\n        this.allExpanded = true\n        return\n      }\n\n      // 检查是否所有题目都是展开状态\n      const allExpanded = this.parsedQuestions.every(question => !question.collapsed)\n      // 检查是否所有题目都是收起状态\n      const allCollapsed = this.parsedQuestions.every(question => question.collapsed)\n\n      if (allExpanded) {\n        this.allExpanded = true\n      } else if (allCollapsed) {\n        this.allExpanded = false\n      }\n      // 如果部分展开部分收起，保持当前状态不变\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 导入成功后清除缓存\n        this.clearCache()\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n        console.error('导入失败', error)\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n        console.warn('CKEditor未加载，使用普通文本框')\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n          console.error('编辑器容器不存在')\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n            console.error('CKEditor未加载或不可用')\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              // 错误处理和事件监听\n              on: {\n                pluginsLoaded: function() {\n                  // CKEditor插件加载完成\n                },\n                instanceReady: function() {\n                  // CKEditor实例准备就绪\n\n                  const editor = evt.editor\n\n                  // 简单的对话框处理 - 参考您提供的代码风格\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      // 图像对话框打开\n\n                      // 简单检查上传完成并切换标签页\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n\n                              // 切换到图像信息标签页\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n\n                        // 10秒后停止检查\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                },\n\n              }\n            })\n          } catch (error) {\n            console.error('CKEditor完整配置初始化失败，尝试简化配置:', error)\n\n            // 尝试简化配置\n            try {\n              this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n                height: 'calc(100vh - 200px)',\n                toolbar: [\n                  ['Bold', 'Italic', 'Underline', 'Strike'],\n                  ['NumberedList', 'BulletedList'],\n                  ['Outdent', 'Indent'],\n                  ['Undo', 'Redo'],\n                  ['Link', 'Unlink'],\n                  ['Image', 'RemoveFormat', 'Maximize']\n                ],\n                removeButtons: '',\n                language: 'zh-cn',\n                removePlugins: 'elementspath',\n                resize_enabled: false,\n                extraPlugins: 'image',\n                allowedContent: true,\n                // 图像上传配置 - 参考您提供的标准配置\n                filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n                image_previewText: ' ',\n                // 设置基础路径，让相对路径能正确解析到后端服务器\n                baseHref: 'http://localhost:8802/',\n                // 隐藏不需要的标签页，只保留上传和图像信息\n                removeDialogTabs: 'image:Link;image:advanced',\n                // 添加实例就绪事件处理\n                on: {\n                  instanceReady: function(evt) {\n                    // CKEditor简化配置实例准备就绪\n\n                    const editor = evt.editor\n\n                    // 监听对话框显示事件\n                    editor.on('dialogShow', function(evt) {\n                      const dialog = evt.data\n                      if (dialog.getName() === 'image') {\n                        // 简化配置：图像对话框打开\n\n                        // 简单检查上传完成并切换标签页\n                        setTimeout(() => {\n                          const checkInterval = setInterval(() => {\n                            try {\n                              const urlField = dialog.getContentElement('info', 'txtUrl')\n                              if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                                clearInterval(checkInterval)\n\n                                // 切换到图像信息标签页\n                                dialog.selectPage('info')\n                              }\n                            } catch (e) {\n                              // 忽略错误\n                            }\n                          }, 500)\n\n                          // 10秒后停止检查\n                          setTimeout(() => clearInterval(checkInterval), 10000)\n                        }, 1000)\n                      }\n                    })\n\n\n                  }\n                }\n              })\n              console.log('CKEditor简化配置初始化成功')\n            } catch (fallbackError) {\n              console.error('CKEditor简化配置也失败，使用普通文本框:', fallbackError)\n              this.showFallbackEditor = true\n              return\n            }\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n              this.lastSaveTime = new Date().toLocaleString()\n\n              // 标记有未保存的更改并保存到缓存\n              this.hasUnsavedChanges = true\n              this.saveToCache()\n            })\n          }\n\n          // 监听按键事件\n          this.richEditor.on('key', () => {\n            setTimeout(() => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n              // 标记有未保存的更改并保存到缓存\n              this.hasUnsavedChanges = true\n              this.saveToCache()\n            }, 100)\n          })\n\n          // 监听实例准备就绪\n          this.richEditor.on('instanceReady', () => {\n            this.editorInitialized = true\n            console.log('CKEditor初始化成功')\n\n            // 优先加载缓存的HTML内容，如果没有则使用documentContent\n            const contentToLoad = this.documentHtmlContent || this.documentContent\n            if (contentToLoad) {\n              console.log('📦 加载内容到编辑器:', contentToLoad.substring(0, 100) + '...')\n              this.richEditor.setData(contentToLoad)\n            }\n          })\n        })\n\n      } catch (error) {\n        console.error('富文本编辑器初始化失败:', error)\n        // 如果CKEditor初始化失败，回退到普通文本框\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = this.documentContent || ''\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value // 纯文本模式下HTML内容与文本内容相同\n          this.lastSaveTime = new Date().toLocaleString()\n\n          // 标记有未保存的更改并保存到缓存\n          this.hasUnsavedChanges = true\n          this.saveToCache()\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n    // 去除HTML标签\n    stripHtmlTags(html) {\n      const div = document.createElement('div')\n      div.innerHTML = html\n      return div.textContent || div.innerText || ''\n    },\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      console.log('🔍 setEditorContent 输入:', content)\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        // 如果编辑器还未初始化，保存内容等待初始化完成后设置\n        this.documentContent = content\n        this.documentHtmlContent = content // 同时设置HTML内容\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        // 为每个题目添加collapsed属性\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n\n        // 更新保存时间\n        this.lastSaveTime = new Date().toLocaleString()\n      } catch (error) {\n        console.error('解析失败', error)\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n        console.warn('⚠️ 解析内容为空或格式不正确')\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n        // 保留图片标签，只移除其他HTML标签\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          console.warn('⚠️ 处理后的内容为空')\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        // 按行分割内容\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n        console.log('总行数:', lines.length)\n\n        if (lines.length === 0) {\n          console.warn('⚠️ 没有有效的内容行')\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n        console.log('前5行内容:', lines.slice(0, 5))\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n                console.error(`❌ 题目 ${questionNumber} 解析失败:`, error)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n              console.log(`✅ 解析最后题目 ${questions.length}:`, parsedQuestion.questionContent?.substring(0, 50) + '...')\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n            console.error(`❌ 最后题目 ${questionNumber} 解析失败:`, error)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n        console.error('❌ 文档解析失败:', error)\n      }\n\n      console.log('解析完成，共', questions.length, '道题目，', errors.length, '个错误')\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析，并尝试推断题型\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n        // 尝试根据题目内容推断题型\n        questionType = this.inferQuestionType(lines)\n        console.log('🔍 未找到题型标注，推断题型为:', questionType)\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      // 支持答案在行首或行中的情况\n      return /^答案[.:：、]\\s*/.test(line) || /.*?\\s+答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 处理img标签中的相对路径\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          // 如果已经是完整路径，不处理\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          // 如果是相对路径，添加后端服务器地址\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          const result = `<img${before}src=\"${fullSrc}\"${after}>`\n          return result\n        })\n\n        return processedContent\n      } catch (error) {\n        console.error('❌ 处理图片路径时出错:', error)\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        console.error('❌ preserveRichTextFormatting 出错:', error)\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 先保存所有图片标签\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        // 移除其他HTML标签，但保留换行\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')  // br标签转换为换行\n          .replace(/<\\/p>/gi, '\\n')       // p结束标签转换为换行\n          .replace(/<p[^>]*>/gi, '\\n')    // p开始标签转换为换行\n          .replace(/<[^>]*>/g, '')        // 移除其他HTML标签\n          .replace(/\\n\\s*\\n/g, '\\n')      // 合并多个换行\n\n        // 恢复图片标签\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        console.error('❌ stripHtmlTagsKeepImages 出错:', error)\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        console.warn('⚠️ 解析选项参数无效')\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 解析选项时出错:', error)\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        // 支持答案在行首或行中的情况\n        const answerMatch = line.match(/(?:^|.*?\\s+)答案[.:：、]\\s*(.+?)(?:\\s|$)/) || line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          } else {\n            console.warn('⚠️ 不支持的难度级别:', difficulty, '，已忽略')\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 从题干提取答案时出错:', error)\n      }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n        console.error('❌ 解析答案值时出错:', error)\n        return answerText || ''\n      }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题干（移除题号）\n      const firstLine = lines[0]\n      let questionContent = ''\n      let currentLineIndex = 0\n\n      // 如果第一行包含题号，移除题号部分\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．、]\\s*(.*)/)\n      if (numberMatch) {\n        questionContent = numberMatch[2].trim() // 移除题号，只保留题干\n        currentLineIndex = 1\n      } else {\n        // 如果第一行不包含题号，直接作为题干，但仍需清理可能的题号\n        questionContent = this.removeQuestionNumber(firstLine).trim()\n        currentLineIndex = 1\n      }\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度 - 只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题',\n        1: '单选题',\n        2: '多选题',\n        3: '判断题'\n      }\n      return typeMap[type] || '单选题'\n    },\n\n\n\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n\n    // ==================== 缓存保存相关方法 ====================\n\n    // 加载缓存的内容\n    loadCachedContent() {\n      try {\n        const cachedData = localStorage.getItem(this.cacheKey)\n        if (cachedData) {\n          const data = JSON.parse(cachedData)\n\n          // 恢复内容\n          this.documentContent = data.documentContent || ''\n          this.documentHtmlContent = data.documentHtmlContent || ''\n          this.lastSaveTime = data.lastSaveTime || ''\n\n          // 如果有内容，标记为有未保存的更改\n          if (this.documentContent || this.documentHtmlContent) {\n            this.hasUnsavedChanges = true\n          }\n        }\n      } catch (error) {\n        console.error('❌ 加载缓存内容失败:', error)\n      }\n    },\n\n    // 保存内容到缓存\n    saveToCache() {\n      // 防抖保存，避免频繁写入\n      if (this.autoSaveTimer) {\n        clearTimeout(this.autoSaveTimer)\n      }\n\n      this.autoSaveTimer = setTimeout(() => {\n        this.saveToCacheNow()\n      }, 2000) // 2秒后保存\n    },\n\n    // 立即保存到缓存\n    saveToCacheNow() {\n      try {\n        const dataToSave = {\n          documentContent: this.documentContent || '',\n          documentHtmlContent: this.documentHtmlContent || '',\n          lastSaveTime: this.lastSaveTime || new Date().toLocaleString(),\n          timestamp: Date.now()\n        }\n\n        localStorage.setItem(this.cacheKey, JSON.stringify(dataToSave))\n        console.log('💾 内容已保存到缓存')\n        this.hasUnsavedChanges = false\n      } catch (error) {\n        console.error('❌ 保存到缓存失败:', error)\n      }\n    },\n\n    // 清除缓存\n    clearCache() {\n      try {\n        localStorage.removeItem(this.cacheKey)\n        this.hasUnsavedChanges = false\n        console.log('🗑️ 缓存已清除')\n      } catch (error) {\n        console.error('❌ 清除缓存失败:', error)\n      }\n    },\n\n    // 页面关闭前的处理\n    handleBeforeUnload(event) {\n      if (this.hasUnsavedChanges) {\n        // 立即保存到缓存\n        this.saveToCacheNow()\n\n        // 提示用户有未保存的更改\n        const message = '您有未保存的更改，确定要离开吗？'\n        event.returnValue = message\n        return message\n      }\n    },\n\n    // 手动保存\n    manualSave() {\n      this.saveToCacheNow()\n      this.$message.success('内容已保存到本地缓存')\n    },\n\n    // 处理缓存相关命令\n    handleCacheCommand(command) {\n      switch (command) {\n        case 'clear':\n          this.$confirm('确定要清除所有缓存的草稿内容吗？', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            this.clearCache()\n            this.documentContent = ''\n            this.documentHtmlContent = ''\n            this.lastSaveTime = ''\n\n            // 清空编辑器内容\n            if (this.richEditor && this.editorInitialized) {\n              this.richEditor.setData('')\n            }\n\n            this.$message.success('缓存已清除')\n          }).catch(() => {\n            // 用户取消\n          })\n          break\n        case 'export':\n          this.exportDraft()\n          break\n      }\n    },\n\n    // 导出草稿\n    exportDraft() {\n      if (!this.documentHtmlContent && !this.documentContent) {\n        this.$message.warning('没有可导出的草稿内容')\n        return\n      }\n\n      const content = this.documentHtmlContent || this.documentContent\n      const blob = new Blob([content], { type: 'text/html;charset=utf-8' })\n      const url = URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = url\n      link.download = `题目草稿_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.html`\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      URL.revokeObjectURL(url)\n\n      this.$message.success('草稿已导出')\n    },\n\n    // ==================== 原有方法 ====================\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      // 清理多余的空行和空白字符\n      content = this.cleanupQuestionContent(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 清理题目内容中的多余空行和空白字符\n    cleanupQuestionContent(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        return content\n          // 移除空的段落标签\n          .replace(/<p[^>]*>\\s*<\\/p>/gi, '')\n          // 移除段落间多余的空白\n          .replace(/(<\\/p>)\\s*(<p[^>]*>)/gi, '$1$2')\n          // 移除开头和结尾的空白\n          .trim()\n      } else {\n        // 处理纯文本内容\n        return content\n          // 移除多余的空行（保留单个换行）\n          .replace(/\\n\\s*\\n\\s*\\n/g, '\\n\\n')\n          // 移除行首行尾空白\n          .replace(/^\\s+|\\s+$/gm, '')\n          // 移除开头和结尾的空白\n          .trim()\n      }\n    },\n\n    // 清理题目内容中的题号、题型标注和答案\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号、题型标注和答案\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/<p[^>]*>(\\s*\\[.*?题\\]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>') // 清理题型标注\n                     .replace(/<p[^>]*>(.*?)\\s+答案[.:：、]\\s*.*?<\\/p>/gi, '<p>$1</p>') // 清理同行答案\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/^(\\s*\\[.*?题\\]\\s*)/, '') // 清理开头的题型标注\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n                     .replace(/>\\s*\\[.*?题\\]\\s*/g, '>') // 清理标签后的题型标注\n      } else {\n        // 对于纯文本内容，直接清理开头的题号、题型标注和同行答案\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '') // 清理题号\n                     .replace(/^\\s*\\[.*?题\\]\\s*/, '') // 清理题型标注\n                     .replace(/\\s+答案[.:：、]\\s*.*$/, '') // 清理同行答案\n                     .trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        // 如果没有找到匹配的段落，返回原始内容\n        return plainContent\n      } catch (error) {\n        console.error('提取HTML题目内容失败:', error)\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干和答案同一行显示 */\n.question-main-line {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  flex: 1;\n  margin: 0;\n}\n\n.question-answer-inline {\n  flex-shrink: 0;\n  color: #409eff;\n  font-weight: 500;\n  background: #f0f9ff;\n  padding: 2px 8px;\n  border-radius: 4px;\n  font-size: 13px;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n.question-answer {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.question-explanation {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #909399;\n}\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n/* 题目内容区域样式 */\n.question-main-content {\n  margin-bottom: 12px;\n  line-height: 1.6;\n  font-size: 14px;\n}\n\n/* 题目内容中的段落样式 */\n.question-main-content p {\n  margin: 0 0 8px 0;\n  line-height: 1.6;\n}\n\n.question-main-content p:last-child {\n  margin-bottom: 0;\n}\n\n/* 题目选项样式 */\n.question-options {\n  margin: 8px 0;\n}\n\n.option-item {\n  margin: 4px 0;\n  line-height: 1.5;\n  font-size: 14px;\n}\n\n/* 答案区域样式 */\n.question-answer-section {\n  margin-top: 12px;\n  padding: 8px 12px;\n  background: #f0f9ff;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.question-answer-label {\n  font-weight: bold;\n  color: #409eff;\n  margin-right: 8px;\n}\n\n.question-answer-value {\n  color: #333;\n  font-weight: 500;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n\n/* 工具栏样式优化 */\n.toolbar {\n  padding: 10px 15px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.toolbar .orange {\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 8px;\n}\n\n/* 题型标签样式 */\n.question-type-tag {\n  margin-left: 8px;\n  font-size: 14px;\n  color: #409eff;\n  font-weight: normal;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4iBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,eAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAL,OAAA;AAAA,IAAAM,QAAA,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,cAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,IAAA;IACA,OAAAA,IAAA;MACA;MACAC,MAAA;MACAC,QAAA;MACA;MACAC,UAAA;QACAC,KAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAJ,KAAA;MACAK,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,iBAAA;IAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,uBAEA,sBACA,6BACA,4BAEA,+BACA,kCACA,8BAEA,8BACA,6BACA,mCAEA,YAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,qBAEA,4BACA,wBACA,oBACA,oBAEA,+BAEA,wBACA,iBAEA,gDACA,4BACA,YAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,iCACA,8BACA,yBAEA,4BAEA,qBACA,yBACA;MACAmB,OAAA;MACAC,cAAA;IACA,iBAEAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,yDACA;MACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA,kBACA,mBAEA,WAAAV,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,uBACA;EAEA;EAEA4B,KAAA;IACA;IACAC,eAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,SAAAC,oBAAA;UACA;QACA;QAIA,IAAAD,MAAA,IAAAA,MAAA,CAAAE,IAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAAC,eAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAC,SAAA;IACA;IACA;IACAC,mBAAA;MACAR,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAQ,KAAA;QACA,IAAAR,MAAA;UACA;UACA,KAAAS,SAAA;YACAD,KAAA,CAAAE,cAAA;UACA;QACA;UACA;UACA,SAAAC,UAAA;YACA,KAAAA,UAAA,CAAAC,OAAA;YACA,KAAAD,UAAA;YACA,KAAAE,iBAAA;UACA;QACA;MACA;MACAP,SAAA;IACA;EACA;EAEAQ,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA;IACA,KAAAZ,qBAAA,QAAAa,QAAA,MAAAC,aAAA;IACA;IACA,KAAAC,UAAA;MACAhD,MAAA,OAAAA;IACA;IACA,KAAAiD,aAAA;MACA1B,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EAEAwB,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,iBAAA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,sBAAAC,kBAAA;EACA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,KAAAC,cAAA;;IAEA;IACA,SAAAC,aAAA;MACAC,YAAA,MAAAD,aAAA;IACA;;IAEA;IACAL,MAAA,CAAAO,mBAAA,sBAAAL,kBAAA;;IAEA;IACA,SAAAb,UAAA;MACA,KAAAA,UAAA,CAAAC,OAAA;MACA,KAAAD,UAAA;IACA;EACA;EACAmB,OAAA,GAAArE,QAAA;IACA;IACAsD,QAAA,WAAAA,SAAA;MACA,IAAAgB,kBAAA,QAAAC,MAAA,CAAAC,KAAA;QAAA/D,MAAA,GAAA6D,kBAAA,CAAA7D,MAAA;QAAAC,QAAA,GAAA4D,kBAAA,CAAA5D,QAAA;MACA,KAAAD,MAAA;QACA,KAAAgE,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MACA,KAAAlE,MAAA,GAAAA,MAAA;MACA,KAAAC,QAAA,GAAAA,QAAA;MACA,KAAAO,WAAA,CAAAR,MAAA,GAAAA,MAAA;MACA,KAAAmE,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAF,MAAA,WAAAA,OAAA;MACA,KAAAG,OAAA,CAAAC,IAAA;IACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAI,MAAA;MACA;MACA,IAAAC,MAAA,QAAAC,kBAAA,MAAAjE,WAAA;MACA,IAAAkE,sBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,MAAA,CAAAhE,YAAA,GAAAqE,QAAA,CAAAC,IAAA;QACAN,MAAA,CAAApE,KAAA,GAAAyE,QAAA,CAAAzE,KAAA;MACA,GAAA2E,KAAA,WAAAb,KAAA;QACAc,OAAA,CAAAd,KAAA,aAAAA,KAAA;QACAM,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAD,MAAA;MACA,IAAAQ,eAAA,OAAAC,cAAA,CAAAhE,OAAA,MAAAuD,MAAA;;MAEA;MACA,IAAAQ,eAAA,CAAArE,YAAA;QACA,IAAAuE,OAAA;UACA;UACA;UACA;QACA;QACAF,eAAA,CAAArE,YAAA,GAAAuE,OAAA,CAAAF,eAAA,CAAArE,YAAA,KAAAqE,eAAA,CAAArE,YAAA;MACA;;MAEA;MACA,IAAAqE,eAAA,CAAApE,UAAA;QACA,IAAAuE,aAAA;UACA;UACA;UACA;QACA;QACAH,eAAA,CAAApE,UAAA,GAAAuE,aAAA,CAAAH,eAAA,CAAApE,UAAA,KAAAoE,eAAA,CAAApE,UAAA;MACA;;MAEA;MACAwE,MAAA,CAAAC,IAAA,CAAAL,eAAA,EAAAM,OAAA,WAAAC,GAAA;QACA,IAAAP,eAAA,CAAAO,GAAA,YAAAP,eAAA,CAAAO,GAAA,cAAAP,eAAA,CAAAO,GAAA,MAAAC,SAAA;UACA,OAAAR,eAAA,CAAAO,GAAA;QACA;MACA;MAEA,OAAAP,eAAA;IACA;IACA;IACAZ,aAAA,WAAAA,cAAA;MAAA,IAAAqB,MAAA;MACA,IAAAC,+BAAA,OAAA1F,MAAA,EAAA2E,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAAvF,UAAA,GAAA0E,QAAA,CAAA9E,IAAA;MACA,GAAAgF,KAAA,WAAAb,KAAA;QACAc,OAAA,CAAAd,KAAA,aAAAA,KAAA;QACA;QACAwB,MAAA,CAAAvF,UAAA;UACAC,KAAA;UACAC,YAAA;UACAC,cAAA;UACAC,QAAA;QACA;MACA;IACA;IACA;IACAqF,iBAAA,WAAAA,kBAAA;MACA,KAAAC,kBAAA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MACA,KAAAC,qBAAA;IACA;IACA;IACAC,2BAAA,WAAAA,4BAAA;MACA,KAAAD,qBAAA;MACA,KAAA3B,eAAA;MACA,KAAAC,aAAA;MACA,KAAAJ,QAAA,CAAAgC,OAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAC,mBAAA,GAAAD,IAAA;MACA,KAAAE,mBAAA;MACA,KAAAC,mBAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAxF,SAAA,SAAAA,SAAA;MACA,UAAAA,SAAA;QACA,KAAAyF,iBAAA;MACA;IACA;IAIA;IACAC,qBAAA,WAAAA,sBAAA;MACA,SAAAzF,iBAAA,CAAA0F,MAAA;QACA,KAAAzC,QAAA,CAAA0C,OAAA;QACA;MACA;MACA,KAAA1C,QAAA,CAAA2C,IAAA,6BAAAC,MAAA,MAAA7F,iBAAA,CAAA0F,MAAA;MACA;IACA;IAEA;IACAI,qBAAA,WAAAA,sBAAA;MACA,KAAAC,aAAA,SAAAA,aAAA;MACA,SAAAA,aAAA;QACA;QACA,KAAA/F,iBAAA,QAAAR,YAAA,CAAAwG,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,UAAA;QAAA;QACA,KAAAjD,QAAA,CAAAgC,OAAA,uBAAAY,MAAA,MAAA7F,iBAAA,CAAA0F,MAAA;MACA;QACA;QACA,KAAA1F,iBAAA;QACA,KAAAiD,QAAA,CAAAgC,OAAA;MACA;IACA;IAIA;IACAkB,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAApG,iBAAA,CAAA0F,MAAA;QACA,KAAAzC,QAAA,CAAA0C,OAAA;QACA;MACA;MAEA,KAAAU,QAAA,+CAAAR,MAAA,MAAA7F,iBAAA,CAAA0F,MAAA;QACAY,iBAAA;QACAC,gBAAA;QACApB,IAAA;MACA,GAAAvB,IAAA;QACA;QACA;QACA,IAAA4C,cAAA,GAAAJ,MAAA,CAAApG,iBAAA,CAAAgG,GAAA,WAAAE,UAAA;UAAA,OACA,IAAAO,qBAAA,EAAAP,UAAA;QAAA,CACA;QAEAQ,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAA5C,IAAA;UACAwC,MAAA,CAAAnD,QAAA,CAAAgC,OAAA,6BAAAY,MAAA,CAAAO,MAAA,CAAApG,iBAAA,CAAA0F,MAAA;UACAU,MAAA,CAAApG,iBAAA;UACAoG,MAAA,CAAAQ,WAAA;UACAR,MAAA,CAAAhD,eAAA;UACAgD,MAAA,CAAA/C,aAAA;QACA,GAAAU,KAAA,WAAAb,KAAA;UACAc,OAAA,CAAAd,KAAA,WAAAA,KAAA;UACAkD,MAAA,CAAAnD,QAAA,CAAAC,KAAA;QACA;MACA;IACA;EAAA,OAAAjD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA1B,QAAA,gCAAA2H,kBAAA,EAGA;IAAA,IAAAU,MAAA;IACA,SAAA7G,iBAAA,CAAA0F,MAAA;MACA,KAAAzC,QAAA,CAAA0C,OAAA;MACA;IACA;IAEA,KAAAU,QAAA,+CAAAR,MAAA,MAAA7F,iBAAA,CAAA0F,MAAA;MACAY,iBAAA;MACAC,gBAAA;MACApB,IAAA;IACA,GAAAvB,IAAA;MACA;MACA,IAAA4C,cAAA,GAAAK,MAAA,CAAA7G,iBAAA,CAAAgG,GAAA,WAAAE,UAAA;QAAA,OACA,IAAAO,qBAAA,EAAAP,UAAA;MAAA,CACA;MAEAQ,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAA5C,IAAA;QACAiD,MAAA,CAAA5D,QAAA,CAAAgC,OAAA,6BAAAY,MAAA,CAAAgB,MAAA,CAAA7G,iBAAA,CAAA0F,MAAA;QACAmB,MAAA,CAAA7G,iBAAA;QACA6G,MAAA,CAAAd,aAAA;QACAc,MAAA,CAAAzD,eAAA;QACAyD,MAAA,CAAAxD,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QACAc,OAAA,CAAAd,KAAA,WAAAA,KAAA;QACA2D,MAAA,CAAA5D,QAAA,CAAAC,KAAA;MACA;IACA,GAAAa,KAAA;MACA8C,MAAA,CAAA5D,QAAA,CAAA2C,IAAA;IACA;EACA,qCAGAkB,qBAAAZ,UAAA,EAAAa,QAAA;IACA,IAAAA,QAAA;MACA,UAAA/G,iBAAA,CAAAgH,QAAA,CAAAd,UAAA;QACA,KAAAlG,iBAAA,CAAAiH,IAAA,CAAAf,UAAA;MACA;IACA;MACA,IAAAgB,KAAA,QAAAlH,iBAAA,CAAAmH,OAAA,CAAAjB,UAAA;MACA,IAAAgB,KAAA;QACA,KAAAlH,iBAAA,CAAAoH,MAAA,CAAAF,KAAA;MACA;IACA;;IAEA;IACA,KAAAnB,aAAA,QAAA/F,iBAAA,CAAA0F,MAAA,UAAAlG,YAAA,CAAAkG,MAAA;EACA,mCAEA2B,mBAAAnB,UAAA;IACA,IAAAgB,KAAA,QAAA1B,iBAAA,CAAA2B,OAAA,CAAAjB,UAAA;IACA,IAAAgB,KAAA;MACA,KAAA1B,iBAAA,CAAA4B,MAAA,CAAAF,KAAA;IACA;MACA,KAAA1B,iBAAA,CAAAyB,IAAA,CAAAf,UAAA;IACA;EACA,mCAEAoB,mBAAAC,QAAA;IACA,KAAAlC,mBAAA,GAAAkC,QAAA;IACA,KAAAnC,mBAAA,GAAAmC,QAAA,CAAA3H,YAAA;IACA,KAAA0F,mBAAA;EACA,mCAEAkC,mBAAAD,QAAA;IACA;IACA,IAAAE,cAAA,OAAAvD,cAAA,CAAAhE,OAAA,MAAAgE,cAAA,CAAAhE,OAAA,MACAqH,QAAA;MACArB,UAAA;MAAA;MACAwB,UAAA;MACAC,UAAA;MACAC,QAAA;MACAC,QAAA;IAAA,EACA;;IAEA;IACA,KAAAxC,mBAAA,GAAAoC,cAAA;IACA,KAAArC,mBAAA,QAAA0C,2BAAA,CAAAP,QAAA,CAAA3H,YAAA;IACA,KAAA0F,mBAAA;EACA,4CAGAwC,4BAAA3C,IAAA;IACA,IAAAhB,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAgB,IAAA,KAAAA,IAAA;EACA,qCAEA4C,qBAAAR,QAAA;IAAA,IAAAS,MAAA;IACA,IAAAlI,eAAA,GAAAyH,QAAA,CAAAzH,eAAA,CAAAmI,OAAA;IACA,IAAAC,cAAA,GAAApI,eAAA,CAAA4F,MAAA,QAAA5F,eAAA,CAAAqI,SAAA,kBAAArI,eAAA;IACA,KAAAuG,QAAA,0CAAAR,MAAA,CAAAqC,cAAA;MACA5B,iBAAA;MACAC,gBAAA;MACApB,IAAA;IACA,GAAAvB,IAAA;MACA,IAAA6C,qBAAA,EAAAc,QAAA,CAAArB,UAAA,EAAAtC,IAAA;QACAoE,MAAA,CAAA/E,QAAA,CAAAgC,OAAA;QACA+C,MAAA,CAAA5E,eAAA;QACA4E,MAAA,CAAA3E,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QACAc,OAAA,CAAAd,KAAA,WAAAA,KAAA;QACA8E,MAAA,CAAA/E,QAAA,CAAAC,KAAA;MACA;IACA;EACA,0CAEAkF,0BAAA;IACA,KAAA9C,mBAAA;IACA,KAAAlC,eAAA;IACA,KAAAC,aAAA;EACA,yCAEAgF,yBAAA;IACA,KAAAxD,kBAAA;IACA,KAAAvD,mBAAA;IACA,KAAA8B,eAAA;IACA,KAAAC,aAAA;EACA,kCAKAiF,kBAAAC,IAAA;IACAA,IAAA;EACA,QAAAtI,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA1B,QAAA,uCAGAgK,yBAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,WAAA;IACA,KAAAC,SAAA;;IAEA;IACA,KAAAnH,SAAA;MACA,IAAAoH,eAAA,GAAAH,MAAA,CAAAI,KAAA,CAAAC,cAAA;MACA,IAAAF,eAAA;QACAA,eAAA,CAAAG,UAAA;MACA;IACA;IAEA,KAAAC,2BAAA;EAEA,gCAGAC,gBAAA;IACA,KAAAC,aAAA;IACA,KAAAC,kBAAA;EACA,oCAGAC,oBAAA;IAAA,IAAAC,MAAA;IACA;IACA,IAAAC,YAAA,2sDAoBArI,IAAA;;IAEA;IACA,SAAAS,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAA6H,OAAA,CAAAD,YAAA;IAEA;MACA;MACA,KAAA9H,SAAA;QACA,IAAA6H,MAAA,CAAA3H,UAAA,IAAA2H,MAAA,CAAAzH,iBAAA;UACAyH,MAAA,CAAA3H,UAAA,CAAA6H,OAAA,CAAAD,YAAA;QAEA;MACA;IACA;;IAEA;IACA,KAAAH,kBAAA;;IAEA;IACA,KAAAlG,QAAA,CAAAgC,OAAA;EAGA,sCAGAuE,sBAAA;IACA,KAAAC,QAAA;EACA,qCAGAC,qBAAA;IACA,KAAAD,QAAA;EACA,6BAGAE,aAAAC,IAAA;IAGA,IAAAC,WAAA,GAAAD,IAAA,CAAAzE,IAAA,kFACAyE,IAAA,CAAAzE,IAAA,4EACAyE,IAAA,CAAAnL,IAAA,CAAAqL,QAAA,aAAAF,IAAA,CAAAnL,IAAA,CAAAqL,QAAA;IACA,IAAAC,OAAA,GAAAH,IAAA,CAAAI,IAAA;IAEA,KAAAH,WAAA;MACA,KAAA5G,QAAA,CAAAC,KAAA;MACA;IACA;IACA,KAAA6G,OAAA;MACA,KAAA9G,QAAA,CAAAC,KAAA;MACA;IACA;;IAEA;IACA,KAAAjB,UAAA,CAAAhD,MAAA,QAAAA,MAAA;;IAEA;IACA,KAAAyJ,WAAA;IACA,KAAAC,SAAA;IAIA;EACA,oCAGAsB,oBAAApG,QAAA,EAAA+F,IAAA;IAAA,IAAAM,MAAA;IAGA,IAAArG,QAAA,CAAAsG,IAAA;MACA;MACA,KAAAzB,WAAA;MACA,KAAAC,SAAA;;MAIA;MACA,KAAAxH,eAAA;MACA,KAAAC,WAAA;;MAEA;MACAgJ,UAAA;QACAF,MAAA,CAAAlB,2BAAA;QACAkB,MAAA,CAAAvB,SAAA;MACA;;MAEA;MACA,KAAA3H,oBAAA;;MAEA;MACA,IAAA6C,QAAA,CAAAwG,SAAA,IAAAxG,QAAA,CAAAwG,SAAA,CAAA3E,MAAA;QACA,KAAAvE,eAAA,GAAA0C,QAAA,CAAAwG,SAAA,CAAArE,GAAA,WAAAuB,QAAA;UAAA,WAAArD,cAAA,CAAAhE,OAAA,MAAAgE,cAAA,CAAAhE,OAAA,MACAqH,QAAA;YACA+C,SAAA;UAAA;QAAA,CACA;QACA;QACA,KAAAC,WAAA;QACA,KAAAnJ,WAAA,GAAAyC,QAAA,CAAA2G,MAAA;;QAEA;QACA,IAAAC,UAAA,GAAA5G,QAAA,CAAA2G,MAAA,GAAA3G,QAAA,CAAA2G,MAAA,CAAA9E,MAAA;QACA,IAAA+E,UAAA;UACA,KAAAxH,QAAA,CAAAgC,OAAA,mCAAAY,MAAA,CAAAhC,QAAA,CAAAwG,SAAA,CAAA3E,MAAA,sCAAAG,MAAA,CAAA4E,UAAA;QACA;UACA,KAAAxH,QAAA,CAAAgC,OAAA,mCAAAY,MAAA,CAAAhC,QAAA,CAAAwG,SAAA,CAAA3E,MAAA;QACA;MAGA;QACA,KAAAzC,QAAA,CAAAC,KAAA;QACA,KAAA/B,eAAA;QACA,KAAAC,WAAA,GAAAyC,QAAA,CAAA2G,MAAA;QAEAxG,OAAA,CAAAd,KAAA;UACAsH,MAAA,EAAA3G,QAAA,CAAA2G,MAAA;UACA3G,QAAA,EAAAA;QACA;MACA;;MAEA;MACA,IAAAA,QAAA,CAAA6G,eAAA;QACA,KAAAC,gBAAA,CAAA9G,QAAA,CAAA6G,eAAA;QACA,KAAA7J,eAAA,GAAAgD,QAAA,CAAA6G,eAAA;QACA,KAAAE,mBAAA,GAAA/G,QAAA,CAAA6G,eAAA;QACA,KAAAG,YAAA,OAAAC,IAAA,GAAAC,cAAA;MACA;;MAEA;MACAX,UAAA;QACAF,MAAA,CAAAlJ,oBAAA;MACA;IACA;MACAgD,OAAA,CAAAd,KAAA,YAAAW,QAAA;MACA,KAAAZ,QAAA,CAAAC,KAAA,CAAAW,QAAA,CAAAmH,GAAA;MACA;MACA,KAAAtC,WAAA;MACA,KAAAC,SAAA;IACA;EACA,kCAGAsC,kBAAA/H,KAAA,EAAA0G,IAAA;IACA5F,OAAA,CAAAd,KAAA,UAAAA,KAAA,EAAA0G,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAnL,IAAA;IACA,KAAAwE,QAAA,CAAAC,KAAA;;IAEA;IACA,KAAAwF,WAAA;IACA,KAAAC,SAAA;EACA,4BAGAuC,YAAA;IAAA,IAAAC,MAAA;IACA,KAAAhK,eAAA,CAAAoD,OAAA,WAAAgD,QAAA;MACA4D,MAAA,CAAAC,IAAA,CAAA7D,QAAA;IACA;EACA,+BAGA8D,eAAAnE,KAAA;IACA,IAAAK,QAAA,QAAApG,eAAA,CAAA+F,KAAA;IACA,KAAAkE,IAAA,CAAA7D,QAAA,gBAAAA,QAAA,CAAA+C,SAAA;;IAEA;IACA,KAAAgB,sBAAA;EACA,QAAArL,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA1B,QAAA,iCAGA+M,mBAAA;IAAA,IAAAC,MAAA;IACA,KAAAjB,WAAA,SAAAA,WAAA;IACA,KAAApJ,eAAA,CAAAoD,OAAA,WAAAgD,QAAA;MACAiE,MAAA,CAAAJ,IAAA,CAAA7D,QAAA,gBAAAiE,MAAA,CAAAjB,WAAA;IACA;EACA,uCAGAe,uBAAA;IACA,SAAAnK,eAAA,CAAAuE,MAAA;MACA,KAAA6E,WAAA;MACA;IACA;;IAEA;IACA,IAAAA,WAAA,QAAApJ,eAAA,CAAAsK,KAAA,WAAAlE,QAAA;MAAA,QAAAA,QAAA,CAAA+C,SAAA;IAAA;IACA;IACA,IAAAoB,YAAA,QAAAvK,eAAA,CAAAsK,KAAA,WAAAlE,QAAA;MAAA,OAAAA,QAAA,CAAA+C,SAAA;IAAA;IAEA,IAAAC,WAAA;MACA,KAAAA,WAAA;IACA,WAAAmB,YAAA;MACA,KAAAnB,WAAA;IACA;IACA;EACA,8BAGAoB,cAAA;IAAA,IAAAC,OAAA;IACA,SAAAzK,eAAA,CAAAuE,MAAA;MACA,KAAAzC,QAAA,CAAA0C,OAAA;MACA;IACA;IAEA,KAAAU,QAAA,6BAAAR,MAAA,MAAA1E,eAAA,CAAAuE,MAAA;MACAY,iBAAA;MACAC,gBAAA;MACApB,IAAA;IACA,GAAAvB,IAAA;MACAgI,OAAA,CAAAC,eAAA;IACA,GAAA9H,KAAA;EACA,gCAGA8H,gBAAA;IAAA,IAAAC,OAAA;IAAA,WAAAC,kBAAA,CAAA7L,OAAA,mBAAA8L,aAAA,CAAA9L,OAAA,IAAA+L,CAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,UAAA,EAAAvI,QAAA,EAAAwI,EAAA;MAAA,WAAAL,aAAA,CAAA9L,OAAA,IAAAoM,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAEA;YACAN,iBAAA,OAAAO,mBAAA,CAAAxM,OAAA,EAAA4L,OAAA,CAAA3K,eAAA;YAEA,IAAA2K,OAAA,CAAAa,aAAA,CAAAxM,OAAA;cACAgM,iBAAA,CAAAhM,OAAA;YACA;;YAEA;YACAiM,UAAA;cACAnN,MAAA,EAAA6M,OAAA,CAAA7M,MAAA;cACAoL,SAAA,EAAA8B,iBAAA;cACA/L,cAAA,EAAA0L,OAAA,CAAAa,aAAA,CAAAvM;YACA;YAAAmM,QAAA,CAAAC,CAAA;YAAA,OAEA,IAAAI,kCAAA,EAAAR,UAAA;UAAA;YAAAvI,QAAA,GAAA0I,QAAA,CAAAM,CAAA;YAAA,MAEAhJ,QAAA,CAAAsG,IAAA;cAAAoC,QAAA,CAAAC,CAAA;cAAA;YAAA;YACAV,OAAA,CAAA7I,QAAA,CAAAgC,OAAA,6BAAAY,MAAA,CAAAsG,iBAAA,CAAAzG,MAAA;YAAA6G,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAA,MAEA,IAAAM,KAAA,CAAAjJ,QAAA,CAAAmH,GAAA;UAAA;YAEAc,OAAA,CAAAxK,mBAAA;YACAwK,OAAA,CAAAjL,eAAA;YACAiL,OAAA,CAAAlB,mBAAA;YACAkB,OAAA,CAAA3K,eAAA;YACA2K,OAAA,CAAA1K,WAAA;;YAEA;YACA0K,OAAA,CAAAiB,UAAA;YAEAjB,OAAA,CAAA1I,eAAA;YACA0I,OAAA,CAAAzI,aAAA;YAAAkJ,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAAAJ,EAAA,GAAAE,QAAA,CAAAM,CAAA;YAEA7I,OAAA,CAAAd,KAAA,SAAAmJ,EAAA;YACAP,OAAA,CAAA7I,QAAA,CAAAC,KAAA;UAAA;YAAA,OAAAqJ,QAAA,CAAAS,CAAA;QAAA;MAAA,GAAAd,OAAA;IAAA;EAEA,+BAGAzK,eAAA;IAAA,IAAAwL,OAAA;IACA,SAAArL,iBAAA;MACA;IACA;;IAEA;IACA,KAAAS,MAAA,CAAA6K,QAAA;MACAlJ,OAAA,CAAAmJ,IAAA;MACA,KAAAC,kBAAA;MACA;IACA;IAEA;MACA;MACA,SAAA1L,UAAA;QACA,KAAAA,UAAA,CAAAC,OAAA;QACA,KAAAD,UAAA;MACA;;MAEA;MACA,IAAA2L,eAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,eAAA;QACArJ,OAAA,CAAAd,KAAA;QACA;MACA;;MAEA;MACAmK,eAAA,CAAAG,SAAA;;MAEA;MACA,KAAAhM,SAAA;QACA;QACA,KAAAa,MAAA,CAAA6K,QAAA,KAAA7K,MAAA,CAAA6K,QAAA,CAAAjF,OAAA;UACAjE,OAAA,CAAAd,KAAA;UACA+J,OAAA,CAAAQ,kBAAA;UACA;QACA;QAEA;UACA;UACAR,OAAA,CAAAvL,UAAA,GAAAW,MAAA,CAAA6K,QAAA,CAAAjF,OAAA,6BAAAhI,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;YACAwN,MAAA;YAAA;YACAC,OAAA,GACA;cAAAlP,IAAA;cAAAmP,KAAA;YAAA,GACA;cAAAnP,IAAA;cAAAmP,KAAA;YAAA,GACA;cAAAnP,IAAA;cAAAmP,KAAA;YAAA,GACA;cAAAnP,IAAA;cAAAmP,KAAA;YAAA,GACA;cAAAnP,IAAA;cAAAmP,KAAA;YAAA,GACA;cAAAnP,IAAA;cAAAmP,KAAA;YAAA,GACA;cAAAnP,IAAA;cAAAmP,KAAA;YAAA,GACA;cAAAnP,IAAA;cAAAmP,KAAA;YAAA,GACA;cAAAnP,IAAA;cAAAmP,KAAA;YAAA,EACA;YACAC,aAAA;YACAC,QAAA;YACAC,aAAA;YACAC,cAAA;YACAC,YAAA;YACAC,cAAA;YACA;YACAC,cAAA;YACAC,qBAAA;YACA;YACAC,sBAAA;YACAC,kBAAA;YACA;YACAC,oBAAA,EAAAlO,OAAA,CAAAC,GAAA,CAAAC,gBAAA;YACAiO,iBAAA;YACA;YACAC,QAAA;UAAA,wBAEA,uCACA,2BAEA,oCAEA;YACAC,aAAA,WAAAA,cAAA;cACA;YAAA,CACA;YACAC,aAAA,WAAAA,cAAA;cACA;;cAEA,IAAAC,MAAA,GAAAC,GAAA,CAAAD,MAAA;;cAEA;cACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;gBACA,IAAAE,MAAA,GAAAF,GAAA,CAAA9P,IAAA;gBACA,IAAAgQ,MAAA,CAAAC,OAAA;kBACA;;kBAEA;kBACA5E,UAAA;oBACA,IAAA6E,aAAA,GAAAC,WAAA;sBACA;wBACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;wBACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;0BACAC,aAAA,CAAAN,aAAA;;0BAEA;0BACAF,MAAA,CAAAS,UAAA;wBACA;sBACA,SAAAC,CAAA;wBACA;sBAAA;oBAEA;;oBAEA;oBACArF,UAAA;sBAAA,OAAAmF,aAAA,CAAAN,aAAA;oBAAA;kBACA;gBACA;cACA;YACA;UAEA,EACA;QACA,SAAA/L,KAAA;UACAc,OAAA,CAAAd,KAAA,8BAAAA,KAAA;;UAEA;UACA;YACA+J,OAAA,CAAAvL,UAAA,GAAAW,MAAA,CAAA6K,QAAA,CAAAjF,OAAA;cACAyF,MAAA;cACAC,OAAA,GACA,2CACA,kCACA,uBACA,kBACA,oBACA,sCACA;cACAE,aAAA;cACAC,QAAA;cACAC,aAAA;cACAC,cAAA;cACAC,YAAA;cACAC,cAAA;cACA;cACAK,oBAAA,EAAAlO,OAAA,CAAAC,GAAA,CAAAC,gBAAA;cACAiO,iBAAA;cACA;cACAC,QAAA;cACA;cACAiB,gBAAA;cACA;cACAZ,EAAA;gBACAH,aAAA,WAAAA,cAAAE,GAAA;kBACA;;kBAEA,IAAAD,MAAA,GAAAC,GAAA,CAAAD,MAAA;;kBAEA;kBACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;oBACA,IAAAE,MAAA,GAAAF,GAAA,CAAA9P,IAAA;oBACA,IAAAgQ,MAAA,CAAAC,OAAA;sBACA;;sBAEA;sBACA5E,UAAA;wBACA,IAAA6E,aAAA,GAAAC,WAAA;0BACA;4BACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;4BACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;8BACAC,aAAA,CAAAN,aAAA;;8BAEA;8BACAF,MAAA,CAAAS,UAAA;4BACA;0BACA,SAAAC,CAAA;4BACA;0BAAA;wBAEA;;wBAEA;wBACArF,UAAA;0BAAA,OAAAmF,aAAA,CAAAN,aAAA;wBAAA;sBACA;oBACA;kBACA;gBAGA;cACA;YACA;YACAjL,OAAA,CAAA2L,GAAA;UACA,SAAAC,aAAA;YACA5L,OAAA,CAAAd,KAAA,6BAAA0M,aAAA;YACA3C,OAAA,CAAAQ,kBAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAR,OAAA,CAAAvL,UAAA,IAAAuL,OAAA,CAAAvL,UAAA,CAAAoN,EAAA;UACA7B,OAAA,CAAAvL,UAAA,CAAAoN,EAAA;YACA,IAAAe,UAAA,GAAA5C,OAAA,CAAAvL,UAAA,CAAAoO,OAAA;YACA,IAAAC,uBAAA,GAAA9C,OAAA,CAAA+C,qBAAA,CAAAH,UAAA;;YAEA;YACA5C,OAAA,CAAArC,mBAAA,GAAAqC,OAAA,CAAAgD,0BAAA,CAAAF,uBAAA;YACA;YACA9C,OAAA,CAAApM,eAAA,GAAAoM,OAAA,CAAAiD,uBAAA,CAAAH,uBAAA;YACA9C,OAAA,CAAApC,YAAA,OAAAC,IAAA,GAAAC,cAAA;;YAEA;YACAkC,OAAA,CAAAkD,iBAAA;YACAlD,OAAA,CAAAmD,WAAA;UACA;QACA;;QAEA;QACAnD,OAAA,CAAAvL,UAAA,CAAAoN,EAAA;UACA1E,UAAA;YACA,IAAAyF,UAAA,GAAA5C,OAAA,CAAAvL,UAAA,CAAAoO,OAAA;YACA,IAAAC,uBAAA,GAAA9C,OAAA,CAAA+C,qBAAA,CAAAH,UAAA;;YAEA;YACA5C,OAAA,CAAArC,mBAAA,GAAAqC,OAAA,CAAAgD,0BAAA,CAAAF,uBAAA;YACA;YACA9C,OAAA,CAAApM,eAAA,GAAAoM,OAAA,CAAAiD,uBAAA,CAAAH,uBAAA;;YAEA;YACA9C,OAAA,CAAAkD,iBAAA;YACAlD,OAAA,CAAAmD,WAAA;UACA;QACA;;QAEA;QACAnD,OAAA,CAAAvL,UAAA,CAAAoN,EAAA;UACA7B,OAAA,CAAArL,iBAAA;UACAoC,OAAA,CAAA2L,GAAA;;UAEA;UACA,IAAAU,aAAA,GAAApD,OAAA,CAAArC,mBAAA,IAAAqC,OAAA,CAAApM,eAAA;UACA,IAAAwP,aAAA;YACArM,OAAA,CAAA2L,GAAA,iBAAAU,aAAA,CAAAlI,SAAA;YACA8E,OAAA,CAAAvL,UAAA,CAAA6H,OAAA,CAAA8G,aAAA;UACA;QACA;MACA;IAEA,SAAAnN,KAAA;MACAc,OAAA,CAAAd,KAAA,iBAAAA,KAAA;MACA;MACA,KAAAkK,kBAAA;IACA;EACA,mCAGAA,mBAAA;IAAA,IAAAkD,OAAA;IACA,IAAAjD,eAAA,GAAAC,QAAA,CAAAC,cAAA;IACA,IAAAF,eAAA;MACA,IAAAkD,QAAA,GAAAjD,QAAA,CAAAkD,aAAA;MACAD,QAAA,CAAAE,SAAA;MACAF,QAAA,CAAAG,WAAA;MACAH,QAAA,CAAAI,KAAA,QAAA9P,eAAA;MACA0P,QAAA,CAAAK,KAAA,CAAAC,OAAA;;MAEA;MACAN,QAAA,CAAAjO,gBAAA,oBAAAmN,CAAA;QACAa,OAAA,CAAAzP,eAAA,GAAA4O,CAAA,CAAAqB,MAAA,CAAAH,KAAA;QACAL,OAAA,CAAA1F,mBAAA,GAAA6E,CAAA,CAAAqB,MAAA,CAAAH,KAAA;QACAL,OAAA,CAAAzF,YAAA,OAAAC,IAAA,GAAAC,cAAA;;QAEA;QACAuF,OAAA,CAAAH,iBAAA;QACAG,OAAA,CAAAF,WAAA;MACA;MAEA/C,eAAA,CAAAG,SAAA;MACAH,eAAA,CAAA0D,WAAA,CAAAR,QAAA;MACA,KAAA3O,iBAAA;IACA;EACA,8BAGAoP,cAAAC,IAAA;IACA,IAAAC,GAAA,GAAA5D,QAAA,CAAAkD,aAAA;IACAU,GAAA,CAAA1D,SAAA,GAAAyD,IAAA;IACA,OAAAC,GAAA,CAAAC,WAAA,IAAAD,GAAA,CAAAE,SAAA;EACA,iCAGAzG,iBAAA0G,OAAA;IACArN,OAAA,CAAA2L,GAAA,4BAAA0B,OAAA;IACA,SAAA3P,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAA6H,OAAA,CAAA8H,OAAA;IACA;MACA;MACA,KAAAxQ,eAAA,GAAAwQ,OAAA;MACA,KAAAzG,mBAAA,GAAAyG,OAAA;IACA;EACA,yBAKAtP,SAAAuP,IAAA,EAAAC,IAAA;IACA,IAAAC,OAAA;IACA,gBAAAC,iBAAA;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAjM,MAAA,EAAAkM,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;MAAA;MACA,IAAAC,KAAA,YAAAA,MAAA;QACApP,YAAA,CAAA6O,OAAA;QACAF,IAAA,CAAAU,KAAA,SAAAJ,IAAA;MACA;MACAjP,YAAA,CAAA6O,OAAA;MACAA,OAAA,GAAApH,UAAA,CAAA2H,KAAA,EAAAR,IAAA;IACA;EACA,sCAGAvB,sBAAAqB,OAAA;IACA,KAAAA,OAAA,SAAAA,OAAA;;IAEA;IACA,IAAAY,aAAA,GAAA5P,MAAA,CAAA6P,QAAA,CAAAC,MAAA;IACA,IAAAC,QAAA,OAAAC,MAAA,CAAAJ,aAAA,CAAAhK,OAAA;IAEA,OAAAoJ,OAAA,CAAApJ,OAAA,CAAAmK,QAAA;EACA,QAAAnS,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA1B,QAAA,4BAGAwD,cAAA;IACA,UAAAnB,eAAA,CAAAI,IAAA;MACA,KAAAE,eAAA;MACA,KAAAC,WAAA;MACA;IACA;IAEA;MACA,IAAAkR,WAAA,QAAAC,oBAAA,MAAA1R,eAAA;MACA;MACA,KAAAM,eAAA,GAAAmR,WAAA,CAAAjI,SAAA,CAAArE,GAAA,WAAAuB,QAAA;QAAA,WAAArD,cAAA,CAAAhE,OAAA,MAAAgE,cAAA,CAAAhE,OAAA,MACAqH,QAAA;UACA+C,SAAA;QAAA;MAAA,CACA;MACA,KAAAlJ,WAAA,GAAAkR,WAAA,CAAA9H,MAAA;;MAEA;MACA,KAAAK,YAAA,OAAAC,IAAA,GAAAC,cAAA;IACA,SAAA7H,KAAA;MACAc,OAAA,CAAAd,KAAA,SAAAA,KAAA;MACA,KAAA9B,WAAA,cAAA8B,KAAA,CAAAsP,OAAA;MACA,KAAArR,eAAA;IACA;EACA,qCAGAoR,qBAAAlB,OAAA;IACA,IAAAhH,SAAA;IACA,IAAAG,MAAA;IAEA,KAAA6G,OAAA,WAAAA,OAAA;MACArN,OAAA,CAAAmJ,IAAA;MACA;QAAA9C,SAAA,EAAAA,SAAA;QAAAG,MAAA;MAAA;IACA;IAEA;MAEA;MACA,IAAA2G,WAAA,QAAAjB,uBAAA,CAAAmB,OAAA;MAEA,KAAAF,WAAA,IAAAA,WAAA,CAAAlQ,IAAA,GAAAyE,MAAA;QACA1B,OAAA,CAAAmJ,IAAA;QACA;UAAA9C,SAAA,EAAAA,SAAA;UAAAG,MAAA;QAAA;MACA;;MAEA;MACA,IAAAiI,KAAA,GAAAtB,WAAA,CAAAuB,KAAA,OAAA1M,GAAA,WAAA2M,IAAA;QAAA,OAAAA,IAAA,CAAA1R,IAAA;MAAA,GAAA2R,MAAA,WAAAD,IAAA;QAAA,OAAAA,IAAA,CAAAjN,MAAA;MAAA;MACA1B,OAAA,CAAA2L,GAAA,SAAA8C,KAAA,CAAA/M,MAAA;MAEA,IAAA+M,KAAA,CAAA/M,MAAA;QACA1B,OAAA,CAAAmJ,IAAA;QACA;UAAA9C,SAAA,EAAAA,SAAA;UAAAG,MAAA;QAAA;MACA;MAEAxG,OAAA,CAAA2L,GAAA,WAAA8C,KAAA,CAAAI,KAAA;MAEA,IAAAC,oBAAA;MACA,IAAAC,cAAA;MAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAP,KAAA,CAAA/M,MAAA,EAAAsN,CAAA;QACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;;QAEA;QACA,IAAAC,eAAA,QAAAC,mBAAA,CAAAP,IAAA,UAAAQ,mBAAA,CAAAR,IAAA;QAEA,IAAAM,eAAA;UACA;UACA,IAAAH,oBAAA,CAAApN,MAAA;YACA;cACA,IAAA0N,YAAA,GAAAN,oBAAA,CAAAO,IAAA;cACA,IAAAC,cAAA,QAAAC,sBAAA,CAAAH,YAAA,EAAAL,cAAA;cACA,IAAAO,cAAA;gBACAjJ,SAAA,CAAApD,IAAA,CAAAqM,cAAA;cACA;YACA,SAAApQ,KAAA;cACAsH,MAAA,CAAAvD,IAAA,WAAApB,MAAA,CAAAkN,cAAA,uCAAAlN,MAAA,CAAA3C,KAAA,CAAAsP,OAAA;cACAxO,OAAA,CAAAd,KAAA,wBAAA2C,MAAA,CAAAkN,cAAA,iCAAA7P,KAAA;YACA;UACA;;UAEA;UACA4P,oBAAA,IAAAH,IAAA;UACAI,cAAA;QACA;UACA;UACA,IAAAD,oBAAA,CAAApN,MAAA;YACAoN,oBAAA,CAAA7L,IAAA,CAAA0L,IAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAG,oBAAA,CAAApN,MAAA;QACA;UACA,IAAA0N,aAAA,GAAAN,oBAAA,CAAAO,IAAA;UACA,IAAAC,eAAA,QAAAC,sBAAA,CAAAH,aAAA,EAAAL,cAAA;UACA,IAAAO,eAAA;YAAA,IAAAE,qBAAA;YACAnJ,SAAA,CAAApD,IAAA,CAAAqM,eAAA;YACAtP,OAAA,CAAA2L,GAAA,gDAAA9J,MAAA,CAAAwE,SAAA,CAAA3E,MAAA,UAAA8N,qBAAA,GAAAF,eAAA,CAAAxT,eAAA,cAAA0T,qBAAA,uBAAAA,qBAAA,CAAArL,SAAA;UACA;QACA,SAAAjF,KAAA;UACAsH,MAAA,CAAAvD,IAAA,WAAApB,MAAA,CAAAkN,cAAA,uCAAAlN,MAAA,CAAA3C,KAAA,CAAAsP,OAAA;UACAxO,OAAA,CAAAd,KAAA,oCAAA2C,MAAA,CAAAkN,cAAA,iCAAA7P,KAAA;QACA;MACA;IAEA,SAAAA,KAAA;MACAsH,MAAA,CAAAvD,IAAA,0CAAApB,MAAA,CAAA3C,KAAA,CAAAsP,OAAA;MACAxO,OAAA,CAAAd,KAAA,cAAAA,KAAA;IACA;IAEAc,OAAA,CAAA2L,GAAA,WAAAtF,SAAA,CAAA3E,MAAA,UAAA8E,MAAA,CAAA9E,MAAA;IACA;MAAA2E,SAAA,EAAAA,SAAA;MAAAG,MAAA,EAAAA;IAAA;EACA,oCAGA0I,oBAAAP,IAAA;IACA;IACA;IACA;IACA,wBAAAc,IAAA,CAAAd,IAAA;EACA,oCAGAQ,oBAAAR,IAAA;IACA;IACA;IACA,mBAAAc,IAAA,CAAAd,IAAA;EACA,uCAGAY,uBAAAH,YAAA;IACA,IAAAX,KAAA,GAAAW,YAAA,CAAAV,KAAA,OAAA1M,GAAA,WAAA2M,IAAA;MAAA,OAAAA,IAAA,CAAA1R,IAAA;IAAA,GAAA2R,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAAjN,MAAA;IAAA;IAEA,IAAA+M,KAAA,CAAA/M,MAAA;MACA,UAAAoH,KAAA;IACA;IAEA,IAAAlN,YAAA;IACA,IAAAE,eAAA;IACA,IAAA4T,iBAAA;;IAEA;IACA,SAAAV,CAAA,MAAAA,CAAA,GAAAP,KAAA,CAAA/M,MAAA,EAAAsN,CAAA;MACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;MACA,IAAAW,SAAA,GAAAhB,IAAA,CAAAiB,KAAA;MACA,IAAAD,SAAA;QACA,IAAAE,QAAA,GAAAF,SAAA;;QAEA;QACA,IAAAE,QAAA,CAAA7M,QAAA;UACApH,YAAA;QACA,WAAAiU,QAAA,CAAA7M,QAAA;UACApH,YAAA;QACA,WAAAiU,QAAA,CAAA7M,QAAA;UACApH,YAAA;QACA,WAAAiU,QAAA,CAAA7M,QAAA;UACApH,YAAA;QACA,WAAAiU,QAAA,CAAA7M,QAAA;UACApH,YAAA;QACA;;QAGA;QACA,IAAAkU,gBAAA,GAAAnB,IAAA,CAAA1K,OAAA,iBAAAhH,IAAA;QACA,IAAA6S,gBAAA;UACAhU,eAAA,GAAAgU,gBAAA;UACAJ,iBAAA,GAAAV,CAAA;QACA;UACAU,iBAAA,GAAAV,CAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,IAAAU,iBAAA;MACAA,iBAAA;MACA;MACA9T,YAAA,QAAAmU,iBAAA,CAAAtB,KAAA;MACAzO,OAAA,CAAA2L,GAAA,sBAAA/P,YAAA;IACA;;IAEA;IACA,SAAAoT,EAAA,GAAAU,iBAAA,EAAAV,EAAA,GAAAP,KAAA,CAAA/M,MAAA,EAAAsN,EAAA;MACA,IAAAL,KAAA,GAAAF,KAAA,CAAAO,EAAA;;MAEA;MACA,SAAAE,mBAAA,CAAAP,KAAA;QACA;QACA7S,eAAA,GAAA6S,KAAA,CAAA1K,OAAA,uBAAAhH,IAAA;QACAyS,iBAAA,GAAAV,EAAA;QACA;MACA,YAAAlT,eAAA;QACA;QACAA,eAAA,GAAA6S,KAAA;QACAe,iBAAA,GAAAV,EAAA;QACA;MACA;IACA;;IAEA;IACA,SAAAA,GAAA,GAAAU,iBAAA,EAAAV,GAAA,GAAAP,KAAA,CAAA/M,MAAA,EAAAsN,GAAA;MACA,IAAAL,MAAA,GAAAF,KAAA,CAAAO,GAAA;;MAEA;MACA,SAAAgB,YAAA,CAAArB,MAAA,UAAAsB,YAAA,CAAAtB,MAAA,KACA,KAAAuB,iBAAA,CAAAvB,MAAA,UAAAwB,gBAAA,CAAAxB,MAAA;QACA;MACA;;MAEA;MACA,IAAAyB,SAAA,GAAAzB,MAAA;MACA;MACA,SAAAO,mBAAA,CAAAP,MAAA;QACAyB,SAAA,GAAAzB,MAAA,CAAA1K,OAAA,uBAAAhH,IAAA;MACA;MAEA,IAAAmT,SAAA;QACA,IAAAtU,eAAA;UACAA,eAAA,WAAAsU,SAAA;QACA;UACAtU,eAAA,GAAAsU,SAAA;QACA;MACA;IACA;IAGA,KAAAtU,eAAA;MACA,UAAAgN,KAAA;IACA;;IAEA;IACA,IAAAuH,oBAAA,GAAAvU,eAAA,CAAAmB,IAAA;IACA;IACA,wBAAAwS,IAAA,CAAAY,oBAAA;MACAA,oBAAA,GAAAA,oBAAA,CAAApM,OAAA,0BAAAhH,IAAA;IACA;;IAEA;IACA,IAAAoT,oBAAA,CAAArN,QAAA;MACAqN,oBAAA,QAAAC,oBAAA,CAAAD,oBAAA;IACA;IAEA,IAAA9M,QAAA;MACA3H,YAAA,EAAAA,YAAA;MACAuF,IAAA,EAAAvF,YAAA;MACA2U,QAAA,OAAAC,kBAAA,CAAA5U,YAAA;MACAE,eAAA,EAAAuU,oBAAA;MACAhD,OAAA,EAAAgD,oBAAA;MACAxU,UAAA;MAAA;MACA4U,WAAA;MACAC,OAAA;MACAC,aAAA;MACArK,SAAA;IACA;;IAEA;IACA,IAAAsK,YAAA,QAAAC,qBAAA,CAAApC,KAAA;IACAlL,QAAA,CAAAmN,OAAA,GAAAE,YAAA,CAAAF,OAAA;;IAEA;IACA,IAAA9U,YAAA,mBAAA2H,QAAA,CAAAmN,OAAA,CAAAhP,MAAA;MACA;MACA9F,YAAA;MACA2H,QAAA,CAAA3H,YAAA,GAAAA,YAAA;MACA2H,QAAA,CAAApC,IAAA,GAAAvF,YAAA;MACA2H,QAAA,CAAAgN,QAAA,QAAAC,kBAAA,CAAA5U,YAAA;IACA;;IAEA;IACA,KAAAkV,0BAAA,CAAArC,KAAA,EAAAlL,QAAA;;IAEA;IACA,IAAA3H,YAAA,iBAAA2H,QAAA,CAAAoN,aAAA,IAAApN,QAAA,CAAAoN,aAAA,CAAAjP,MAAA;MACA;MACA,kBAAA+N,IAAA,CAAAlM,QAAA,CAAAoN,aAAA;QACA/U,YAAA;QACA2H,QAAA,CAAA3H,YAAA,GAAAA,YAAA;QACA2H,QAAA,CAAApC,IAAA,GAAAvF,YAAA;QACA2H,QAAA,CAAAgN,QAAA,QAAAC,kBAAA,CAAA5U,YAAA;MACA;IACA;;IAEA;IACA2H,QAAA,CAAAzH,eAAA,QAAAwU,oBAAA,CAAA/M,QAAA,CAAAzH,eAAA;IACAyH,QAAA,CAAA8J,OAAA,GAAA9J,QAAA,CAAAzH,eAAA;IAEA,OAAAyH,QAAA;EACA,6BAGAyM,aAAArB,IAAA;IACA;IACA,6BAAAc,IAAA,CAAAd,IAAA;EACA,6BAGAsB,aAAAtB,IAAA;IACA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA,yBAAAc,IAAA,CAAAd,IAAA;EACA,kCAGAuB,kBAAAvB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,iCAGAwB,iBAAAxB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,mCAGA6B,mBAAArP,IAAA;IACA,IAAAhB,OAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAgB,IAAA;EACA,QAAAlF,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA1B,QAAA,gCAGAuW,kBAAA1D,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MAEA;MACA,IAAA2D,gBAAA,GAAA3D,OAAA,CAAApJ,OAAA,mDAAA2L,KAAA,EAAAqB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,SAAAtB,KAAA;;QAEA;QACA,IAAAsB,GAAA,CAAA5F,UAAA,eAAA4F,GAAA,CAAA5F,UAAA,gBAAA4F,GAAA,CAAA5F,UAAA;UACA,OAAAsE,KAAA;QACA;;QAEA;QACA,IAAAwB,OAAA,8BAAAF,GAAA,CAAA5F,UAAA,QAAA4F,GAAA,SAAAA,GAAA;QACA,IAAAG,MAAA,UAAAxP,MAAA,CAAAoP,MAAA,YAAApP,MAAA,CAAAuP,OAAA,QAAAvP,MAAA,CAAAsP,KAAA;QACA,OAAAE,MAAA;MACA;MAEA,OAAAL,gBAAA;IACA,SAAA9R,KAAA;MACAc,OAAA,CAAAd,KAAA,iBAAAA,KAAA;MACA,OAAAmO,OAAA;IACA;EACA,2CAGApB,2BAAAoB,OAAA;IAAA,IAAAiE,OAAA;IACA,KAAAjE,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MACA;MACA,IAAA2D,gBAAA,GAAA3D;MACA;MAAA,CACApJ,OAAA,oDAAA2L,KAAA,EAAAqB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,CAAA5F,UAAA,aAAA4F,GAAA,CAAA5F,UAAA;UACA,IAAA8F,OAAA,GAAAE,OAAA,CAAAP,iBAAA,CAAAG,GAAA;UACA,cAAArP,MAAA,CAAAoP,MAAA,YAAApP,MAAA,CAAAuP,OAAA,QAAAvP,MAAA,CAAAsP,KAAA;QACA;QACA,OAAAvB,KAAA;MACA;MACA;MAAA,CACA3L,OAAA,sBACAA,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACAA,OAAA,sBACAA,OAAA;MAEA,OAAA+M,gBAAA,CAAA/T,IAAA;IACA,SAAAiC,KAAA;MACAc,OAAA,CAAAd,KAAA,qCAAAA,KAAA;MACA,OAAAmO,OAAA;IACA;EACA,wCAGAnB,wBAAAmB,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MAEA;MACA,IAAAkE,MAAA;MACA,IAAAC,UAAA;MACA,IAAAC,uBAAA,GAAApE,OAAA,CAAApJ,OAAA,2BAAA2L,KAAA;QACA2B,MAAA,CAAAtO,IAAA,CAAA2M,KAAA;QACA,gCAAA/N,MAAA,CAAA2P,UAAA;MACA;;MAEA;MACA,IAAArE,WAAA,GAAAsE,uBAAA,CACAxN,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;;MAEA;MACA,IAAAyN,YAAA,GAAAvE,WAAA;MACAoE,MAAA,CAAAhR,OAAA,WAAAoR,GAAA,EAAAzO,KAAA;QACA,IAAAwJ,WAAA,0BAAA7K,MAAA,CAAAqB,KAAA;QACA,IAAAwO,YAAA,CAAA1O,QAAA,CAAA0J,WAAA;UACAgF,YAAA,GAAAA,YAAA,CAAAzN,OAAA,CAAAyI,WAAA,EAAAiF,GAAA;QACA;MACA;MAEA,OAAAD,YAAA,CAAAzU,IAAA;IACA,SAAAiC,KAAA;MACAc,OAAA,CAAAd,KAAA,kCAAAA,KAAA;MACA,OAAAmO,OAAA;IACA;EACA,sCAGAwD,sBAAApC,KAAA,EAAAmD,UAAA;IACA,IAAAlB,OAAA;IAEA,KAAA7C,KAAA,CAAAgE,OAAA,CAAApD,KAAA,KAAAmD,UAAA,QAAAA,UAAA,IAAAnD,KAAA,CAAA/M,MAAA;MACA1B,OAAA,CAAAmJ,IAAA;MACA;QAAAuH,OAAA,EAAAA;MAAA;IACA;IAEA;MACA,SAAA1B,CAAA,GAAA4C,UAAA,EAAA5C,CAAA,GAAAP,KAAA,CAAA/M,MAAA,EAAAsN,CAAA;QACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;QAEA,KAAAL,IAAA,WAAAA,IAAA;UACA;QACA;;QAEA;QACA,IAAAmD,WAAA,GAAAnD,IAAA,CAAAiB,KAAA;QACA,IAAAkC,WAAA;UACA,IAAAC,SAAA,GAAAD,WAAA,IAAAE,WAAA;UACA,IAAAC,aAAA,GAAAH,WAAA,MAAAA,WAAA,IAAA7U,IAAA;UAEA,IAAA8U,SAAA,IAAAE,aAAA;YACAvB,OAAA,CAAAzN,IAAA;cACA8O,SAAA,EAAAA,SAAA;cACAG,KAAA,EAAAH,SAAA;cACAE,aAAA,EAAAA,aAAA;cACA5E,OAAA,EAAA4E;YACA;UACA;QACA,gBAAAhC,YAAA,CAAAtB,IAAA,UAAAuB,iBAAA,CAAAvB,IAAA,UAAAwB,gBAAA,CAAAxB,IAAA;UACA;UACA;QACA;UACA;UACA;UACA,IAAAwD,oBAAA,GAAAxD,IAAA,CAAAiB,KAAA;UACA,IAAAuC,oBAAA;YACA;YACA,IAAAC,aAAA,GAAAzD,IAAA,CAAAD,KAAA;YAAA,IAAA2D,SAAA,OAAAC,2BAAA,CAAApW,OAAA,EACAkW,aAAA;cAAAG,KAAA;YAAA;cAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAA7J,CAAA,IAAAjE,IAAA;gBAAA,IAAAkO,YAAA,GAAAF,KAAA,CAAA5F,KAAA;gBACA,KAAA8F,YAAA;gBAEA,IAAA7C,KAAA,GAAA6C,YAAA,CAAA7C,KAAA;gBACA,IAAAA,KAAA;kBACA,IAAAmC,UAAA,GAAAnC,KAAA,IAAAoC,WAAA;kBACA,IAAAC,cAAA,GAAArC,KAAA,MAAAA,KAAA,IAAA3S,IAAA;kBAEA,IAAA8U,UAAA,IAAAE,cAAA;oBACAvB,OAAA,CAAAzN,IAAA;sBACA8O,SAAA,EAAAA,UAAA;sBACAG,KAAA,EAAAH,UAAA;sBACAE,aAAA,EAAAA,cAAA;sBACA5E,OAAA,EAAA4E;oBACA;kBACA;gBACA;cACA;YAAA,SAAAS,GAAA;cAAAL,SAAA,CAAA5G,CAAA,CAAAiH,GAAA;YAAA;cAAAL,SAAA,CAAAM,CAAA;YAAA;UACA;QACA;MACA;IACA,SAAAzT,KAAA;MACAc,OAAA,CAAAd,KAAA,eAAAA,KAAA;IACA;IAEA;MAAAwR,OAAA,EAAAA;IAAA;EACA,2CAGAI,2BAAArC,KAAA,EAAAlL,QAAA;IACA,SAAAyL,CAAA,MAAAA,CAAA,GAAAP,KAAA,CAAA/M,MAAA,EAAAsN,CAAA;MACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;;MAEA;MACA;MACA,IAAA4D,WAAA,GAAAjE,IAAA,CAAAiB,KAAA,4CAAAjB,IAAA,CAAAiB,KAAA;MACA,IAAAgD,WAAA;QACArP,QAAA,CAAAoN,aAAA,QAAAkC,gBAAA,CAAAD,WAAA,KAAArP,QAAA,CAAA3H,YAAA;QACA;MACA;;MAEA;MACA,IAAAkX,gBAAA,GAAAnE,IAAA,CAAAiB,KAAA;MACA,IAAAkD,gBAAA;QACAvP,QAAA,CAAAkN,WAAA,GAAAqC,gBAAA,IAAA7V,IAAA;QACA;MACA;;MAEA;MACA,IAAA8V,eAAA,GAAApE,IAAA,CAAAiB,KAAA;MACA,IAAAmD,eAAA;QACA,IAAAlX,UAAA,GAAAkX,eAAA;QACA;QACA,IAAAlX,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAAmH,QAAA,CAAAnH,UAAA;UACA0H,QAAA,CAAA1H,UAAA,GAAAA,UAAA;QACA;UACAmE,OAAA,CAAAmJ,IAAA,iBAAAtN,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA;IACA,KAAA0H,QAAA,CAAAoN,aAAA;MACApN,QAAA,CAAAoN,aAAA,QAAAqC,gCAAA,CAAAzP,QAAA,CAAAzH,eAAA,EAAAyH,QAAA,CAAA3H,YAAA;IACA;EACA,iDAGAoX,iCAAAlX,eAAA,EAAAF,YAAA;IACA,KAAAE,eAAA,WAAAA,eAAA;MACA;IACA;IAEA;MACA;MACA,IAAAmX,QAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;MAEA,SAAAC,GAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,GAAA,GAAAC,SAAA,CAAAzR,MAAA,EAAAwR,GAAA;QAAA,IAAAE,OAAA,GAAAD,SAAA,CAAAD,GAAA;QACA,IAAAG,OAAA,GAAAvX,eAAA,CAAA8T,KAAA,CAAAwD,OAAA;QACA,IAAAC,OAAA,IAAAA,OAAA,CAAA3R,MAAA;UACA;UACA,IAAA4R,SAAA,GAAAD,OAAA,CAAAA,OAAA,CAAA3R,MAAA;UACA,IAAA6R,MAAA,GAAAD,SAAA,CAAArP,OAAA,sBAAAhH,IAAA;UAEA,IAAAsW,MAAA;YACA,YAAAV,gBAAA,CAAAU,MAAA,EAAA3X,YAAA;UACA;QACA;MACA;IACA,SAAAsD,KAAA;MACAc,OAAA,CAAAd,KAAA,kBAAAA,KAAA;IACA;IAEA;EACA,iCAGA2T,iBAAAW,UAAA,EAAA5X,YAAA;IACA,KAAA4X,UAAA,WAAAA,UAAA;MACA;IACA;IAEA;MACA,IAAAC,aAAA,GAAAD,UAAA,CAAAvW,IAAA;MAEA,KAAAwW,aAAA;QACA;MACA;MAEA,IAAA7X,YAAA;QACA;QACA,OAAA6X,aAAA;MACA;QACA;QACA,OAAAA,aAAA,CAAAzB,WAAA;MACA;IACA,SAAA9S,KAAA;MACAc,OAAA,CAAAd,KAAA,gBAAAA,KAAA;MACA,OAAAsU,UAAA;IACA;EACA,oCAGAE,oBAAArG,OAAA;IACA,IAAAsG,QAAA;IACA,IAAAC,SAAA;IAEA,IAAAC,SAAA;IACA,IAAAjE,KAAA;IACA,IAAAkE,WAAA;IAEA,QAAAlE,KAAA,GAAAgE,SAAA,CAAAG,IAAA,CAAA1G,OAAA;MACA,IAAAyG,WAAA;QACA;QACAH,QAAA,CAAA1Q,IAAA;UACA9B,IAAA,EAAA2S,WAAA;UACAzG,OAAA,EAAAA,OAAA,CAAAlJ,SAAA,CAAA0P,SAAA,EAAAjE,KAAA,CAAA1M,KAAA,EAAAjG,IAAA;QACA;MACA;MACA6W,WAAA,GAAAlE,KAAA;MACAiE,SAAA,GAAAjE,KAAA,CAAA1M,KAAA,GAAA0M,KAAA,IAAAlO,MAAA;IACA;;IAEA;IACA,IAAAoS,WAAA;MACAH,QAAA,CAAA1Q,IAAA;QACA9B,IAAA,EAAA2S,WAAA;QACAzG,OAAA,EAAAA,OAAA,CAAAlJ,SAAA,CAAA0P,SAAA,EAAA5W,IAAA;MACA;IACA;IAEA,OAAA0W,QAAA;EACA,sCAGAK,sBAAAC,OAAA;IAAA,IAAAC,OAAA;IACA,IAAA7N,SAAA;IACA,IAAAzK,YAAA,QAAAuY,mBAAA,CAAAF,OAAA,CAAA9S,IAAA;;IAEA;IACA,IAAAiT,cAAA,QAAAC,qBAAA,CAAAJ,OAAA,CAAA5G,OAAA;IAEA+G,cAAA,CAAA7T,OAAA,WAAA+T,KAAA,EAAApR,KAAA;MACA;QACA,IAAAK,QAAA,GAAA2Q,OAAA,CAAAK,kBAAA,CAAAD,KAAA,EAAA1Y,YAAA,EAAAsH,KAAA;QACA,IAAAK,QAAA;UACA8C,SAAA,CAAApD,IAAA,CAAAM,QAAA;QACA;MACA,SAAArE,KAAA;QACA,UAAA4J,KAAA,UAAAjH,MAAA,CAAAqB,KAAA,0CAAArB,MAAA,CAAA3C,KAAA,CAAAsP,OAAA;MACA;IACA;IAEA,OAAAnI,SAAA;EACA,sCAGAgO,sBAAAhH,OAAA;IACA,IAAAmH,MAAA;IACA,IAAAC,WAAA;IAEA,IAAAZ,SAAA;IACA,IAAAjE,KAAA;IAEA,QAAAA,KAAA,GAAA6E,WAAA,CAAAV,IAAA,CAAA1G,OAAA;MACA,IAAAwG,SAAA;QACA;QACAW,MAAA,CAAAvR,IAAA,CAAAoK,OAAA,CAAAlJ,SAAA,CAAA0P,SAAA,EAAAjE,KAAA,CAAA1M,KAAA,EAAAjG,IAAA;MACA;MACA4W,SAAA,GAAAjE,KAAA,CAAA1M,KAAA;IACA;;IAEA;IACA,IAAA2Q,SAAA,GAAAxG,OAAA,CAAA3L,MAAA;MACA8S,MAAA,CAAAvR,IAAA,CAAAoK,OAAA,CAAAlJ,SAAA,CAAA0P,SAAA,EAAA5W,IAAA;IACA;IAEA,OAAAuX,MAAA,CAAA5F,MAAA,WAAA0F,KAAA;MAAA,OAAAA,KAAA,CAAA5S,MAAA;IAAA;EACA,QAAAzF,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA1B,QAAA,iCAGA+Z,mBAAAD,KAAA,EAAA1Y,YAAA;IACA,IAAA6S,KAAA,GAAA6F,KAAA,CAAA5F,KAAA,OAAA1M,GAAA,WAAA2M,IAAA;MAAA,OAAAA,IAAA,CAAA1R,IAAA;IAAA,GAAA2R,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAAjN,MAAA;IAAA;IAEA,IAAA+M,KAAA,CAAA/M,MAAA;MACA,UAAAoH,KAAA;IACA;;IAEA;IACA,IAAA4L,SAAA,GAAAjG,KAAA;IACA,IAAA3S,eAAA;IACA,IAAA6Y,gBAAA;;IAEA;IACA,IAAAC,WAAA,GAAAF,SAAA,CAAA9E,KAAA;IACA,IAAAgF,WAAA;MACA9Y,eAAA,GAAA8Y,WAAA,IAAA3X,IAAA;MACA0X,gBAAA;IACA;MACA;MACA7Y,eAAA,QAAAwU,oBAAA,CAAAoE,SAAA,EAAAzX,IAAA;MACA0X,gBAAA;IACA;;IAEA;IACA,OAAAA,gBAAA,GAAAlG,KAAA,CAAA/M,MAAA;MACA,IAAAiN,IAAA,GAAAF,KAAA,CAAAkG,gBAAA;MACA,SAAA3E,YAAA,CAAArB,IAAA;QACA;MACA;MACA7S,eAAA,WAAA6S,IAAA;MACAgG,gBAAA;IACA;IAEA,IAAApR,QAAA;MACA3H,YAAA,EAAAA,YAAA;MACAE,eAAA,EAAAA,eAAA,CAAAmB,IAAA;MACApB,UAAA;MAAA;MACA4U,WAAA;MACAC,OAAA;MACAC,aAAA;IACA;;IAEA;IACA,IAAA/U,YAAA;MACA,IAAAgV,YAAA,QAAAiE,YAAA,CAAApG,KAAA,EAAAkG,gBAAA;MACApR,QAAA,CAAAmN,OAAA,GAAAE,YAAA,CAAAF,OAAA;MACAiE,gBAAA,GAAA/D,YAAA,CAAAkE,SAAA;IACA;;IAEA;IACA,KAAAC,iBAAA,CAAAtG,KAAA,EAAAkG,gBAAA,EAAApR,QAAA;;IAEA;IACAA,QAAA,CAAAzH,eAAA,QAAAwU,oBAAA,CAAA/M,QAAA,CAAAzH,eAAA;IAEA,OAAAyH,QAAA;EACA,6BAAAyM,aAGArB,IAAA;IACA,4BAAAc,IAAA,CAAAd,IAAA;EACA,6BAGAkG,aAAApG,KAAA,EAAAmD,UAAA;IACA,IAAAlB,OAAA;IACA,IAAAsE,YAAA,GAAApD,UAAA;IAEA,OAAAoD,YAAA,GAAAvG,KAAA,CAAA/M,MAAA;MACA,IAAAiN,IAAA,GAAAF,KAAA,CAAAuG,YAAA;MACA,IAAAlD,WAAA,GAAAnD,IAAA,CAAAiB,KAAA;MAEA,KAAAkC,WAAA;QACA;MACA;MAEApB,OAAA,CAAAzN,IAAA;QACA8O,SAAA,EAAAD,WAAA,IAAAE,WAAA;QACAC,aAAA,EAAAH,WAAA,IAAA7U,IAAA;MACA;MAEA+X,YAAA;IACA;IAEA;MAAAtE,OAAA,EAAAA,OAAA;MAAAoE,SAAA,EAAAE;IAAA;EACA,kCAGAD,kBAAAtG,KAAA,EAAAmD,UAAA,EAAArO,QAAA;IACA,SAAAyL,CAAA,GAAA4C,UAAA,EAAA5C,CAAA,GAAAP,KAAA,CAAA/M,MAAA,EAAAsN,CAAA;MACA,IAAAL,IAAA,GAAAF,KAAA,CAAAO,CAAA;;MAEA;MACA,IAAA4D,WAAA,GAAAjE,IAAA,CAAAiB,KAAA;MACA,IAAAgD,WAAA;QACArP,QAAA,CAAAoN,aAAA,QAAAsE,WAAA,CAAArC,WAAA,KAAArP,QAAA,CAAA3H,YAAA;QACA;MACA;;MAEA;MACA,IAAAkX,gBAAA,GAAAnE,IAAA,CAAAiB,KAAA;MACA,IAAAkD,gBAAA;QACAvP,QAAA,CAAAkN,WAAA,GAAAqC,gBAAA,IAAA7V,IAAA;QACA;MACA;;MAEA;MACA,IAAA8V,eAAA,GAAApE,IAAA,CAAAiB,KAAA;MACA,IAAAmD,eAAA;QACA,IAAAlX,UAAA,GAAAkX,eAAA;QACA;QACA,IAAAlX,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAAmH,QAAA,CAAAnH,UAAA;UACA0H,QAAA,CAAA1H,UAAA,GAAAA,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,KAAA0H,QAAA,CAAAoN,aAAA;MACApN,QAAA,CAAAoN,aAAA,QAAAuE,wBAAA,CAAA3R,QAAA,CAAAzH,eAAA,EAAAyH,QAAA,CAAA3H,YAAA;IACA;EACA,yCAKAsZ,yBAAA7H,OAAA,EAAAzR,YAAA;IACA;IACA,IAAAuZ,eAAA,IACA,cACA,iBACA,cACA,eACA;IAEA,SAAAC,GAAA,MAAAC,gBAAA,GAAAF,eAAA,EAAAC,GAAA,GAAAC,gBAAA,CAAA3T,MAAA,EAAA0T,GAAA;MAAA,IAAAhC,OAAA,GAAAiC,gBAAA,CAAAD,GAAA;MACA,IAAA/B,OAAA,OAAA3K,mBAAA,CAAAxM,OAAA,EAAAmR,OAAA,CAAAiI,QAAA,CAAAlC,OAAA;MACA,IAAAC,OAAA,CAAA3R,MAAA;QACA,IAAA6R,MAAA,GAAAF,OAAA,CAAAA,OAAA,CAAA3R,MAAA;QACA,YAAAuT,WAAA,CAAA1B,MAAA,EAAA3X,YAAA;MACA;IACA;IAEA;EACA,oCAGAuY,oBAAAtE,QAAA;IACA,IAAA1P,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAA0P,QAAA;EACA,oCAGA0F,oBAAApU,IAAA;IACA,IAAAhB,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAgB,IAAA;EACA,qCAKAqU,qBAAArU,IAAA;IACA,IAAAsU,QAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,QAAA,CAAAtU,IAAA;EACA,kCAKA/C,kBAAA;IACA;MACA,IAAAsX,UAAA,GAAAC,YAAA,CAAAC,OAAA,MAAAC,QAAA;MACA,IAAAH,UAAA;QACA,IAAA3a,IAAA,GAAA+a,IAAA,CAAAC,KAAA,CAAAL,UAAA;;QAEA;QACA,KAAA7Y,eAAA,GAAA9B,IAAA,CAAA8B,eAAA;QACA,KAAA+J,mBAAA,GAAA7L,IAAA,CAAA6L,mBAAA;QACA,KAAAC,YAAA,GAAA9L,IAAA,CAAA8L,YAAA;;QAEA;QACA,SAAAhK,eAAA,SAAA+J,mBAAA;UACA,KAAAuF,iBAAA;QACA;MACA;IACA,SAAAjN,KAAA;MACAc,OAAA,CAAAd,KAAA,gBAAAA,KAAA;IACA;EACA,4BAGAkN,YAAA;IAAA,IAAA4J,OAAA;IACA;IACA,SAAAtX,aAAA;MACAC,YAAA,MAAAD,aAAA;IACA;IAEA,KAAAA,aAAA,GAAA0H,UAAA;MACA4P,OAAA,CAAAvX,cAAA;IACA;EACA,QAAAxC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA1B,QAAA,6BAGAiE,eAAA;IACA;MACA,IAAAwX,UAAA;QACApZ,eAAA,OAAAA,eAAA;QACA+J,mBAAA,OAAAA,mBAAA;QACAC,YAAA,OAAAA,YAAA,QAAAC,IAAA,GAAAC,cAAA;QACAmP,SAAA,EAAApP,IAAA,CAAAqP,GAAA;MACA;MAEAR,YAAA,CAAAS,OAAA,MAAAP,QAAA,EAAAC,IAAA,CAAAO,SAAA,CAAAJ,UAAA;MACAjW,OAAA,CAAA2L,GAAA;MACA,KAAAQ,iBAAA;IACA,SAAAjN,KAAA;MACAc,OAAA,CAAAd,KAAA,eAAAA,KAAA;IACA;EACA,2BAGA6J,WAAA;IACA;MACA4M,YAAA,CAAAW,UAAA,MAAAT,QAAA;MACA,KAAA1J,iBAAA;MACAnM,OAAA,CAAA2L,GAAA;IACA,SAAAzM,KAAA;MACAc,OAAA,CAAAd,KAAA,cAAAA,KAAA;IACA;EACA,mCAGAX,mBAAAgY,KAAA;IACA,SAAApK,iBAAA;MACA;MACA,KAAA1N,cAAA;;MAEA;MACA,IAAA+P,OAAA;MACA+H,KAAA,CAAAC,WAAA,GAAAhI,OAAA;MACA,OAAAA,OAAA;IACA;EACA,2BAGAiI,WAAA;IACA,KAAAhY,cAAA;IACA,KAAAQ,QAAA,CAAAgC,OAAA;EACA,mCAGAyV,mBAAAC,OAAA;IAAA,IAAAC,OAAA;IACA,QAAAD,OAAA;MACA;QACA,KAAAtU,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACApB,IAAA;QACA,GAAAvB,IAAA;UACAgX,OAAA,CAAA7N,UAAA;UACA6N,OAAA,CAAA/Z,eAAA;UACA+Z,OAAA,CAAAhQ,mBAAA;UACAgQ,OAAA,CAAA/P,YAAA;;UAEA;UACA,IAAA+P,OAAA,CAAAlZ,UAAA,IAAAkZ,OAAA,CAAAhZ,iBAAA;YACAgZ,OAAA,CAAAlZ,UAAA,CAAA6H,OAAA;UACA;UAEAqR,OAAA,CAAA3X,QAAA,CAAAgC,OAAA;QACA,GAAAlB,KAAA;UACA;QAAA,CACA;QACA;MACA;QACA,KAAA8W,WAAA;QACA;IACA;EACA,4BAGAA,YAAA;IACA,UAAAjQ,mBAAA,UAAA/J,eAAA;MACA,KAAAoC,QAAA,CAAA0C,OAAA;MACA;IACA;IAEA,IAAA0L,OAAA,QAAAzG,mBAAA,SAAA/J,eAAA;IACA,IAAAia,IAAA,OAAAC,IAAA,EAAA1J,OAAA;MAAAlM,IAAA;IAAA;IACA,IAAA6V,GAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAJ,IAAA;IACA,IAAAK,IAAA,GAAA7N,QAAA,CAAAkD,aAAA;IACA2K,IAAA,CAAAC,IAAA,GAAAJ,GAAA;IACAG,IAAA,CAAA1R,QAAA,+BAAA5D,MAAA,KAAAiF,IAAA,GAAAuQ,WAAA,GAAAxI,KAAA,QAAA5K,OAAA;IACAqF,QAAA,CAAAgO,IAAA,CAAAvK,WAAA,CAAAoK,IAAA;IACAA,IAAA,CAAAI,KAAA;IACAjO,QAAA,CAAAgO,IAAA,CAAAE,WAAA,CAAAL,IAAA;IACAF,GAAA,CAAAQ,eAAA,CAAAT,GAAA;IAEA,KAAA/X,QAAA,CAAAgC,OAAA;EACA,4CAKAyW,4BAAAnU,QAAA;IACA,KAAAA,QAAA,KAAAA,QAAA,CAAAzH,eAAA;MACA;IACA;IAEA,IAAAuR,OAAA,GAAA9J,QAAA,CAAAzH,eAAA;;IAEA;IACA,SAAA8K,mBAAA,SAAAA,mBAAA,CAAA5D,QAAA;MACA;MACA,IAAA2U,WAAA,QAAAC,uBAAA,CAAArU,QAAA,CAAAzH,eAAA,OAAA8K,mBAAA;MACA,IAAA+Q,WAAA;QACAtK,OAAA,GAAAsK,WAAA;MACA;IACA;;IAEA;IACAtK,OAAA,QAAAiD,oBAAA,CAAAjD,OAAA;;IAEA;IACAA,OAAA,QAAAwK,sBAAA,CAAAxK,OAAA;IAEA,YAAA0D,iBAAA,CAAA1D,OAAA;EACA,uCAGAwK,uBAAAxK,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA,OAAAA,OAAA;IACA;;IAEA;IACA,IAAAA,OAAA,CAAArK,QAAA;MACA,OAAAqK;MACA;MAAA,CACApJ,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACAhH,IAAA;IACA;MACA;MACA,OAAAoQ;MACA;MAAA,CACApJ,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACAhH,IAAA;IACA;EACA,qCAGAqT,qBAAAjD,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA,OAAAA,OAAA;IACA;;IAEA;IACA,IAAAA,OAAA,CAAArK,QAAA;MACA;MACA,OAAAqK,OAAA,CAAApJ,OAAA,wDACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;IACA;MACA;MACA,OAAAoJ,OAAA,CAAApJ,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAhH,IAAA;IACA;EACA,wCAGA2a,wBAAAE,YAAA,EAAAH,WAAA;IACA,KAAAG,YAAA,KAAAH,WAAA;MACA,OAAAG,YAAA;IACA;IAEA;MACA;MACA,IAAAC,SAAA,GAAAD,YAAA,CAAA7T,OAAA,uBAAAhH,IAAA;;MAEA;MACA,IAAA+a,UAAA,GAAAL,WAAA,CAAA/H,KAAA;MAAA,IAAAqI,UAAA,OAAA3F,2BAAA,CAAApW,OAAA,EAEA8b,UAAA;QAAAE,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAzF,CAAA,MAAA0F,MAAA,GAAAD,UAAA,CAAAzP,CAAA,IAAAjE,IAAA;UAAA,IAAA4T,SAAA,GAAAD,MAAA,CAAAvL,KAAA;UACA,IAAAyL,aAAA,GAAAD,SAAA,CAAAlU,OAAA,iBAAAhH,IAAA;UACA;UACA,IAAAob,kBAAA,GAAAD,aAAA,CAAAnU,OAAA,0BAAAhH,IAAA;UACA,IAAAob,kBAAA,CAAArV,QAAA,CAAA+U,SAAA,CAAA5T,SAAA;YACA;YACA,YAAAmM,oBAAA,CAAA6H,SAAA;UACA;QACA;;QAEA;MAAA,SAAAzF,GAAA;QAAAuF,UAAA,CAAAxM,CAAA,CAAAiH,GAAA;MAAA;QAAAuF,UAAA,CAAAtF,CAAA;MAAA;MACA,OAAAmF,YAAA;IACA,SAAA5Y,KAAA;MACAc,OAAA,CAAAd,KAAA,kBAAAA,KAAA;MACA,OAAA4Y,YAAA;IACA;EACA,QAAA7b,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA1B,QAAA,2BAIA8d,aAAA;IACA,KAAA7c,WAAA,CAAAC,OAAA;IACA,KAAA0D,eAAA;EACA,4BAEAmZ,YAAA;IACA,KAAA9c,WAAA,CAAAG,YAAA;IACA,KAAAH,WAAA,CAAAI,UAAA;IACA,KAAAJ,WAAA,CAAAK,eAAA;IACA,KAAAL,WAAA,CAAAC,OAAA;IACA,KAAA0D,eAAA;EACA;AAEA", "ignoreList": []}]}