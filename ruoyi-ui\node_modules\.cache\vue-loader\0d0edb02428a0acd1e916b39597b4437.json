{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\BatchImport.vue?vue&type=template&id=7c007bad&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\BatchImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}