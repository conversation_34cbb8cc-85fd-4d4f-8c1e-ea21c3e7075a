{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=template&id=5888aa98&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}