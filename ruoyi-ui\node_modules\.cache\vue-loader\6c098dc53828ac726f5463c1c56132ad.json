{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\BatchImport.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\BatchImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJwppbXBvcnQgeyBkb3dubG9hZFRlbXBsYXRlLCBwYXJzZUltcG9ydEZpbGUsIGJhdGNoSW1wb3J0UXVlc3Rpb25zIH0gZnJvbSAnQC9hcGkvYml6L3F1ZXN0aW9uJwppbXBvcnQgeyBkb3dubG9hZCB9IGZyb20gJ0AvdXRpbHMvcmVxdWVzdCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQmF0Y2hJbXBvcnQiLAogIHByb3BzOiB7CiAgICB2aXNpYmxlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgYmFua0lkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgZGVmYXVsdE1vZGU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnZG9jdW1lbnQnIC8vIOWPquaUr+aMgWRvY3VtZW50CiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRTdGVwOiAwLAogICAgICBpbXBvcnRNb2RlOiAnZG9jdW1lbnQnLCAvLyDlr7zlhaXmqKHlvI/vvJrlj6rmlK/mjIFkb2N1bWVudAogICAgICB1cGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2NvbW1vbi91cGxvYWQnLAogICAgICB1cGxvYWRIZWFkZXJzOiB7CiAgICAgICAgQXV0aG9yaXphdGlvbjogJ0JlYXJlciAnICsgZ2V0VG9rZW4oKQogICAgICB9LAogICAgICB1cGxvYWRlZEZpbGU6IG51bGwsCiAgICAgIHBhcnNpbmc6IGZhbHNlLAogICAgICBpbXBvcnRpbmc6IGZhbHNlLAogICAgICBwYXJzZWREYXRhOiBbXSwKICAgICAgcGFyc2VFcnJvcnM6IFtdLAogICAgICBpbXBvcnRSZXN1bHQ6IHsKICAgICAgICBzdWNjZXNzQ291bnQ6IDAsCiAgICAgICAgZmFpbENvdW50OiAwLAogICAgICAgIGVycm9yczogW10KICAgICAgfQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHZpc2libGUodmFsKSB7CiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHZhbAogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy5yZXNldEltcG9ydCgpCiAgICAgIH0KICAgIH0sCiAgICBkaWFsb2dWaXNpYmxlKHZhbCkgewogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmlzaWJsZScsIHZhbCkKICAgIH0sCiAgICBkZWZhdWx0TW9kZSh2YWwpIHsKICAgICAgaWYgKHZhbCkgewogICAgICAgIHRoaXMuaW1wb3J0TW9kZSA9IHZhbAogICAgICB9CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CgogICAgLy8g6I635Y+W6aKY5Z6L5ZCN56ewCiAgICBnZXRRdWVzdGlvblR5cGVOYW1lKHR5cGUpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAnc2luZ2xlJzogJ+WNlemAiemimCcsCiAgICAgICAgJ211bHRpcGxlJzogJ+WkmumAiemimCcsCiAgICAgICAgJ2p1ZGdtZW50JzogJ+WIpOaWremimCcKICAgICAgfQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAn5pyq55+l6aKY5Z6LJwogICAgfSwKICAgIC8vIOiOt+WPlumimOWei+minOiJsgogICAgZ2V0UXVlc3Rpb25UeXBlQ29sb3IodHlwZSkgewogICAgICBjb25zdCBjb2xvck1hcCA9IHsKICAgICAgICAnc2luZ2xlJzogJ3ByaW1hcnknLAogICAgICAgICdtdWx0aXBsZSc6ICdzdWNjZXNzJywKICAgICAgICAnanVkZ21lbnQnOiAnd2FybmluZycKICAgICAgfQogICAgICByZXR1cm4gY29sb3JNYXBbdHlwZV0gfHwgJ2luZm8nCiAgICB9LAogICAgLy8g5LiL5LiA5q2lCiAgICBuZXh0U3RlcCgpIHsKICAgICAgdGhpcy5jdXJyZW50U3RlcCsrCiAgICB9LAogICAgLy8g5LiK5LiA5q2lCiAgICBwcmV2U3RlcCgpIHsKICAgICAgdGhpcy5jdXJyZW50U3RlcC0tCiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5YmN6aqM6K+BCiAgICBiZWZvcmVGaWxlVXBsb2FkKGZpbGUpIHsKICAgICAgY29uc3QgaXNMdDEwTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTAKICAgICAgY29uc3QgaXNEb2N4ID0gZmlsZS5uYW1lLnRvTG93ZXJDYXNlKCkuZW5kc1dpdGgoJy5kb2N4JykKCiAgICAgIGlmICghaXNEb2N4KSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5LygLmRvY3jmoLzlvI/nmoRXb3Jk5paH5qGjIScpCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KCiAgICAgIGlmICghaXNMdDEwTSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hzEwTUIhJykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQogICAgICByZXR1cm4gdHJ1ZQogICAgfSwKICAgIC8vIOaWh+S7tuS4iuS8oOaIkOWKnwogICAgaGFuZGxlRmlsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUpIHsKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgIHRoaXMudXBsb2FkZWRGaWxlID0gewogICAgICAgICAgbmFtZTogZmlsZS5uYW1lLAogICAgICAgICAgdXJsOiByZXNwb25zZS51cmwsCiAgICAgICAgICBmaWxlTmFtZTogcmVzcG9uc2UuZmlsZU5hbWUKICAgICAgICB9CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlofku7bkuIrkvKDmiJDlip8nKQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfmlofku7bkuIrkvKDlpLHotKUnKQogICAgICB9CiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5aSx6LSlCiAgICBoYW5kbGVGaWxlRXJyb3IoKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWh+S7tuS4iuS8oOWksei0pScpCiAgICB9LAogICAgLy8g6Kej5p6Q5paH5Lu2CiAgICBwYXJzZUZpbGUoKSB7CiAgICAgIHRoaXMucGFyc2luZyA9IHRydWUKICAgICAgLy8g5paH5qGj5YaF5a656Kej5p6QCiAgICAgIHRoaXMucGFyc2VEb2N1bWVudENvbnRlbnQoKQogICAgfSwKCiAgICAvLyDop6PmnpDmlofmoaPlhoXlrrkKICAgIGFzeW5jIHBhcnNlRG9jdW1lbnRDb250ZW50KCkgewogICAgICB0cnkgewogICAgICAgIC8vIOivu+WPluaWh+S7tuWGheWuuQogICAgICAgIGNvbnN0IGZpbGVDb250ZW50ID0gYXdhaXQgdGhpcy5yZWFkRmlsZUNvbnRlbnQodGhpcy51cGxvYWRlZEZpbGUudXJsKQoKICAgICAgICAvLyDop6PmnpDpopjnm64KICAgICAgICBjb25zdCBwYXJzZVJlc3VsdCA9IHRoaXMucGFyc2VRdWVzdGlvbkNvbnRlbnQoZmlsZUNvbnRlbnQpCgogICAgICAgIHRoaXMucGFyc2luZyA9IGZhbHNlCiAgICAgICAgdGhpcy5wYXJzZWREYXRhID0gcGFyc2VSZXN1bHQucXVlc3Rpb25zCiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHBhcnNlUmVzdWx0LmVycm9ycwogICAgICAgIHRoaXMubmV4dFN0ZXAoKQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIHRoaXMucGFyc2luZyA9IGZhbHNlCiAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q5paH5qGj5YaF5a655aSx6LSlJywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6Kej5p6Q5paH5qGj5YaF5a655aSx6LSlJykKICAgICAgfQogICAgfSwKCiAgICAvLyDor7vlj5bmlofku7blhoXlrrkKICAgIHJlYWRGaWxlQ29udGVudChmaWxlVXJsKSB7CiAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7CiAgICAgICAgY29uc3QgeGhyID0gbmV3IFhNTEh0dHBSZXF1ZXN0KCkKICAgICAgICB4aHIub3BlbignR0VUJywgcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArIGZpbGVVcmwsIHRydWUpCiAgICAgICAgeGhyLnNldFJlcXVlc3RIZWFkZXIoJ0F1dGhvcml6YXRpb24nLCAnQmVhcmVyICcgKyBnZXRUb2tlbigpKQogICAgICAgIHhoci5yZXNwb25zZVR5cGUgPSAndGV4dCcKCiAgICAgICAgeGhyLm9ubG9hZCA9IGZ1bmN0aW9uKCkgewogICAgICAgICAgaWYgKHhoci5zdGF0dXMgPT09IDIwMCkgewogICAgICAgICAgICByZXNvbHZlKHhoci5yZXNwb25zZVRleHQpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICByZWplY3QobmV3IEVycm9yKCfor7vlj5bmlofku7blpLHotKUnKSkKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIHhoci5vbmVycm9yID0gZnVuY3Rpb24oKSB7CiAgICAgICAgICByZWplY3QobmV3IEVycm9yKCfor7vlj5bmlofku7blpLHotKUnKSkKICAgICAgICB9CgogICAgICAgIHhoci5zZW5kKCkKICAgICAgfSkKICAgIH0sCgogICAgLy8g6Kej5p6Q6aKY55uu5YaF5a65CiAgICBwYXJzZVF1ZXN0aW9uQ29udGVudChjb250ZW50KSB7CiAgICAgIGNvbnN0IHF1ZXN0aW9ucyA9IFtdCiAgICAgIGNvbnN0IGVycm9ycyA9IFtdCgogICAgICB0cnkgewogICAgICAgIC8vIOaMiemimOWei+WIhuWJsuWGheWuuQogICAgICAgIGNvbnN0IHNlY3Rpb25zID0gdGhpcy5zcGxpdEJ5UXVlc3Rpb25UeXBlKGNvbnRlbnQpCgogICAgICAgIHNlY3Rpb25zLmZvckVhY2goKHNlY3Rpb24sIHNlY3Rpb25JbmRleCkgPT4gewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgY29uc3QgcGFyc2VkUXVlc3Rpb25zID0gdGhpcy5wYXJzZVNlY3Rpb25RdWVzdGlvbnMoc2VjdGlvbikKICAgICAgICAgICAgcXVlc3Rpb25zLnB1c2goLi4ucGFyc2VkUXVlc3Rpb25zKQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgZXJyb3JzLnB1c2goYOesrCR7c2VjdGlvbkluZGV4ICsgMX3kuKrpopjlnovljLrln5/op6PmnpDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQogICAgICAgICAgfQogICAgICAgIH0pCgogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGVycm9ycy5wdXNoKGDmlofmoaPop6PmnpDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZX1gKQogICAgICB9CgogICAgICByZXR1cm4geyBxdWVzdGlvbnMsIGVycm9ycyB9CiAgICB9LAoKICAgIC8vIOaMiemimOWei+WIhuWJsuWGheWuuQogICAgc3BsaXRCeVF1ZXN0aW9uVHlwZShjb250ZW50KSB7CiAgICAgIGNvbnN0IHNlY3Rpb25zID0gW10KICAgICAgY29uc3QgdHlwZVJlZ2V4ID0gL1xbKOWNlemAiemimHzlpJrpgInpoph85Yik5pat6aKYKVxdL2cKCiAgICAgIGxldCBsYXN0SW5kZXggPSAwCiAgICAgIGxldCBtYXRjaAogICAgICBsZXQgY3VycmVudFR5cGUgPSBudWxsCgogICAgICB3aGlsZSAoKG1hdGNoID0gdHlwZVJlZ2V4LmV4ZWMoY29udGVudCkpICE9PSBudWxsKSB7CiAgICAgICAgaWYgKGN1cnJlbnRUeXBlKSB7CiAgICAgICAgICAvLyDkv53lrZjkuIrkuIDkuKrljLrln58KICAgICAgICAgIHNlY3Rpb25zLnB1c2goewogICAgICAgICAgICB0eXBlOiBjdXJyZW50VHlwZSwKICAgICAgICAgICAgY29udGVudDogY29udGVudC5zdWJzdHJpbmcobGFzdEluZGV4LCBtYXRjaC5pbmRleCkudHJpbSgpCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgICBjdXJyZW50VHlwZSA9IG1hdGNoWzFdCiAgICAgICAgbGFzdEluZGV4ID0gbWF0Y2guaW5kZXggKyBtYXRjaFswXS5sZW5ndGgKICAgICAgfQoKICAgICAgLy8g5L+d5a2Y5pyA5ZCO5LiA5Liq5Yy65Z+fCiAgICAgIGlmIChjdXJyZW50VHlwZSkgewogICAgICAgIHNlY3Rpb25zLnB1c2goewogICAgICAgICAgdHlwZTogY3VycmVudFR5cGUsCiAgICAgICAgICBjb250ZW50OiBjb250ZW50LnN1YnN0cmluZyhsYXN0SW5kZXgpLnRyaW0oKQogICAgICAgIH0pCiAgICAgIH0KCiAgICAgIHJldHVybiBzZWN0aW9ucwogICAgfSwKCiAgICAvLyDop6PmnpDljLrln5/lhoXnmoTpopjnm64KICAgIHBhcnNlU2VjdGlvblF1ZXN0aW9ucyhzZWN0aW9uKSB7CiAgICAgIGNvbnN0IHF1ZXN0aW9ucyA9IFtdCiAgICAgIGNvbnN0IHF1ZXN0aW9uVHlwZSA9IHRoaXMuY29udmVydFF1ZXN0aW9uVHlwZShzZWN0aW9uLnR5cGUpCgogICAgICAvLyDmjInpopjlj7fliIblibLpopjnm64KICAgICAgY29uc3QgcXVlc3Rpb25CbG9ja3MgPSB0aGlzLnNwbGl0QnlRdWVzdGlvbk51bWJlcihzZWN0aW9uLmNvbnRlbnQpCgogICAgICBxdWVzdGlvbkJsb2Nrcy5mb3JFYWNoKChibG9jaywgaW5kZXgpID0+IHsKICAgICAgICB0cnkgewogICAgICAgICAgY29uc3QgcXVlc3Rpb24gPSB0aGlzLnBhcnNlUXVlc3Rpb25CbG9jayhibG9jaywgcXVlc3Rpb25UeXBlLCBpbmRleCArIDEpCiAgICAgICAgICBpZiAocXVlc3Rpb24pIHsKICAgICAgICAgICAgcXVlc3Rpb25zLnB1c2gocXVlc3Rpb24pCiAgICAgICAgICB9CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihg56ysJHtpbmRleCArIDF96aKY6Kej5p6Q5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkKICAgICAgICB9CiAgICAgIH0pCgogICAgICByZXR1cm4gcXVlc3Rpb25zCiAgICB9LAoKICAgIC8vIOaMiemimOWPt+WIhuWJsumimOebrgogICAgc3BsaXRCeVF1ZXN0aW9uTnVtYmVyKGNvbnRlbnQpIHsKICAgICAgY29uc3QgYmxvY2tzID0gW10KICAgICAgY29uc3QgbnVtYmVyUmVnZXggPSAvXlxzKihcZCspWy4677ya77yOXVxzKi9nbQoKICAgICAgbGV0IGxhc3RJbmRleCA9IDAKICAgICAgbGV0IG1hdGNoCgogICAgICB3aGlsZSAoKG1hdGNoID0gbnVtYmVyUmVnZXguZXhlYyhjb250ZW50KSkgIT09IG51bGwpIHsKICAgICAgICBpZiAobGFzdEluZGV4ID4gMCkgewogICAgICAgICAgLy8g5L+d5a2Y5LiK5LiA6aKYCiAgICAgICAgICBibG9ja3MucHVzaChjb250ZW50LnN1YnN0cmluZyhsYXN0SW5kZXgsIG1hdGNoLmluZGV4KS50cmltKCkpCiAgICAgICAgfQogICAgICAgIGxhc3RJbmRleCA9IG1hdGNoLmluZGV4CiAgICAgIH0KCiAgICAgIC8vIOS/neWtmOacgOWQjuS4gOmimAogICAgICBpZiAobGFzdEluZGV4IDwgY29udGVudC5sZW5ndGgpIHsKICAgICAgICBibG9ja3MucHVzaChjb250ZW50LnN1YnN0cmluZyhsYXN0SW5kZXgpLnRyaW0oKSkKICAgICAgfQoKICAgICAgcmV0dXJuIGJsb2Nrcy5maWx0ZXIoYmxvY2sgPT4gYmxvY2subGVuZ3RoID4gMCkKICAgIH0sCgogICAgLy8g6Kej5p6Q5Y2V5Liq6aKY55uu5Z2XCiAgICBwYXJzZVF1ZXN0aW9uQmxvY2soYmxvY2ssIHF1ZXN0aW9uVHlwZSwgcXVlc3Rpb25JbmRleCkgewogICAgICBjb25zdCBsaW5lcyA9IGJsb2NrLnNwbGl0KCdcbicpLm1hcChsaW5lID0+IGxpbmUudHJpbSgpKS5maWx0ZXIobGluZSA9PiBsaW5lLmxlbmd0aCA+IDApCgogICAgICBpZiAobGluZXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfpopjnm67lhoXlrrnkuLrnqbonKQogICAgICB9CgogICAgICAvLyDmj5Dlj5bpopjlj7flkozpopjlubIKICAgICAgY29uc3QgZmlyc3RMaW5lID0gbGluZXNbMF0KICAgICAgY29uc3QgbnVtYmVyTWF0Y2ggPSBmaXJzdExpbmUubWF0Y2goL15ccyooXGQrKVsuOu+8mu+8jl1ccyooLiopLykKICAgICAgaWYgKCFudW1iZXJNYXRjaCkgewogICAgICAgIHRocm93IG5ldyBFcnJvcign6aKY5Y+35qC85byP5LiN5q2j56GuJykKICAgICAgfQoKICAgICAgbGV0IHF1ZXN0aW9uQ29udGVudCA9IG51bWJlck1hdGNoWzJdCiAgICAgIGxldCBjdXJyZW50TGluZUluZGV4ID0gMQoKICAgICAgLy8g57un57ut6K+75Y+W6aKY5bmy5YaF5a6577yI55u05Yiw6YGH5Yiw6YCJ6aG577yJCiAgICAgIHdoaWxlIChjdXJyZW50TGluZUluZGV4IDwgbGluZXMubGVuZ3RoKSB7CiAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2N1cnJlbnRMaW5lSW5kZXhdCiAgICAgICAgaWYgKHRoaXMuaXNPcHRpb25MaW5lKGxpbmUpKSB7CiAgICAgICAgICBicmVhawogICAgICAgIH0KICAgICAgICBxdWVzdGlvbkNvbnRlbnQgKz0gJ1xuJyArIGxpbmUKICAgICAgICBjdXJyZW50TGluZUluZGV4KysKICAgICAgfQoKICAgICAgY29uc3QgcXVlc3Rpb24gPSB7CiAgICAgICAgcXVlc3Rpb25UeXBlOiBxdWVzdGlvblR5cGUsCiAgICAgICAgcXVlc3Rpb25Db250ZW50OiBxdWVzdGlvbkNvbnRlbnQudHJpbSgpLAogICAgICAgIGRpZmZpY3VsdHk6ICfkuK3nrYknLAogICAgICAgIGV4cGxhbmF0aW9uOiAnJywKICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICBjb3JyZWN0QW5zd2VyOiAnJwogICAgICB9CgogICAgICAvLyDop6PmnpDpgInpobnvvIjlr7nkuo7pgInmi6npopjvvIkKICAgICAgaWYgKHF1ZXN0aW9uVHlwZSAhPT0gJ2p1ZGdtZW50JykgewogICAgICAgIGNvbnN0IG9wdGlvblJlc3VsdCA9IHRoaXMucGFyc2VPcHRpb25zKGxpbmVzLCBjdXJyZW50TGluZUluZGV4KQogICAgICAgIHF1ZXN0aW9uLm9wdGlvbnMgPSBvcHRpb25SZXN1bHQub3B0aW9ucwogICAgICAgIGN1cnJlbnRMaW5lSW5kZXggPSBvcHRpb25SZXN1bHQubmV4dEluZGV4CiAgICAgIH0KCiAgICAgIC8vIOino+aekOetlOahiOOAgeino+aekOOAgemavuW6pgogICAgICB0aGlzLnBhcnNlUXVlc3Rpb25NZXRhKGxpbmVzLCBjdXJyZW50TGluZUluZGV4LCBxdWVzdGlvbikKCiAgICAgIHJldHVybiBxdWVzdGlvbgogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrpgInpobnooYwKICAgIGlzT3B0aW9uTGluZShsaW5lKSB7CiAgICAgIHJldHVybiAvXltBLVphLXpdWy4677ya77yOXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDop6PmnpDpgInpobkKICAgIHBhcnNlT3B0aW9ucyhsaW5lcywgc3RhcnRJbmRleCkgewogICAgICBjb25zdCBvcHRpb25zID0gW10KICAgICAgbGV0IGN1cnJlbnRJbmRleCA9IHN0YXJ0SW5kZXgKCiAgICAgIHdoaWxlIChjdXJyZW50SW5kZXggPCBsaW5lcy5sZW5ndGgpIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbY3VycmVudEluZGV4XQogICAgICAgIGNvbnN0IG9wdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXihbQS1aYS16XSlbLjrvvJrvvI5dXHMqKC4qKS8pCgogICAgICAgIGlmICghb3B0aW9uTWF0Y2gpIHsKICAgICAgICAgIGJyZWFrCiAgICAgICAgfQoKICAgICAgICBvcHRpb25zLnB1c2goewogICAgICAgICAgb3B0aW9uS2V5OiBvcHRpb25NYXRjaFsxXS50b1VwcGVyQ2FzZSgpLAogICAgICAgICAgb3B0aW9uQ29udGVudDogb3B0aW9uTWF0Y2hbMl0udHJpbSgpCiAgICAgICAgfSkKCiAgICAgICAgY3VycmVudEluZGV4KysKICAgICAgfQoKICAgICAgcmV0dXJuIHsgb3B0aW9ucywgbmV4dEluZGV4OiBjdXJyZW50SW5kZXggfQogICAgfSwKCiAgICAvLyDop6PmnpDpopjnm67lhYPkv6Hmga/vvIjnrZTmoYjjgIHop6PmnpDjgIHpmr7luqbvvIkKICAgIHBhcnNlUXVlc3Rpb25NZXRhKGxpbmVzLCBzdGFydEluZGV4LCBxdWVzdGlvbikgewogICAgICBmb3IgKGxldCBpID0gc3RhcnRJbmRleDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2ldCgogICAgICAgIC8vIOino+aekOetlOahiAogICAgICAgIGNvbnN0IGFuc3dlck1hdGNoID0gbGluZS5tYXRjaCgvXuetlOahiFvvvJo6XVxzKiguKykvKQogICAgICAgIGlmIChhbnN3ZXJNYXRjaCkgewogICAgICAgICAgcXVlc3Rpb24uY29ycmVjdEFuc3dlciA9IHRoaXMucGFyc2VBbnN3ZXIoYW5zd2VyTWF0Y2hbMV0sIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQoKICAgICAgICAvLyDop6PmnpDop6PmnpAKICAgICAgICBjb25zdCBleHBsYW5hdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXuino+aekFvvvJo6XVxzKiguKykvKQogICAgICAgIGlmIChleHBsYW5hdGlvbk1hdGNoKSB7CiAgICAgICAgICBxdWVzdGlvbi5leHBsYW5hdGlvbiA9IGV4cGxhbmF0aW9uTWF0Y2hbMV0udHJpbSgpCiAgICAgICAgICBjb250aW51ZQogICAgICAgIH0KCiAgICAgICAgLy8g6Kej5p6Q6Zq+5bqmCiAgICAgICAgY29uc3QgZGlmZmljdWx0eU1hdGNoID0gbGluZS5tYXRjaCgvXumavuW6plvvvJo6XVxzKijnroDljZV85Lit562JfOWbsOmavikvKQogICAgICAgIGlmIChkaWZmaWN1bHR5TWF0Y2gpIHsKICAgICAgICAgIHF1ZXN0aW9uLmRpZmZpY3VsdHkgPSBkaWZmaWN1bHR5TWF0Y2hbMV0KICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDlpoLmnpzmsqHmnInmmL7lvI/nrZTmoYjvvIzlsJ3or5Xku47popjlubLkuK3mj5Dlj5YKICAgICAgaWYgKCFxdWVzdGlvbi5jb3JyZWN0QW5zd2VyKSB7CiAgICAgICAgcXVlc3Rpb24uY29ycmVjdEFuc3dlciA9IHRoaXMuZXh0cmFjdEFuc3dlckZyb21Db250ZW50KHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCwgcXVlc3Rpb24ucXVlc3Rpb25UeXBlKQogICAgICB9CiAgICB9LAoKICAgIC8vIOino+aekOetlOahiAogICAgcGFyc2VBbnN3ZXIoYW5zd2VyVGV4dCwgcXVlc3Rpb25UeXBlKSB7CiAgICAgIGlmIChxdWVzdGlvblR5cGUgPT09ICdqdWRnbWVudCcpIHsKICAgICAgICAvLyDliKTmlq3popjnrZTmoYjlpITnkIYKICAgICAgICBpZiAoYW5zd2VyVGV4dC5pbmNsdWRlcygn5q2j56GuJykgfHwgYW5zd2VyVGV4dC5pbmNsdWRlcygn5a+5JykgfHwgYW5zd2VyVGV4dC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCd0cnVlJykpIHsKICAgICAgICAgIHJldHVybiAndHJ1ZScKICAgICAgICB9IGVsc2UgaWYgKGFuc3dlclRleHQuaW5jbHVkZXMoJ+mUmeivrycpIHx8IGFuc3dlclRleHQuaW5jbHVkZXMoJ+mUmScpIHx8IGFuc3dlclRleHQuaW5jbHVkZXMoJ+WBhycpIHx8IGFuc3dlclRleHQudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnZmFsc2UnKSkgewogICAgICAgICAgcmV0dXJuICdmYWxzZScKICAgICAgICB9CiAgICAgICAgcmV0dXJuIGFuc3dlclRleHQudHJpbSgpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g6YCJ5oup6aKY562U5qGI5aSE55CGCiAgICAgICAgcmV0dXJuIGFuc3dlclRleHQucmVwbGFjZSgvWyzvvIxcc10vZywgJycpLnRvVXBwZXJDYXNlKCkKICAgICAgfQogICAgfSwKCiAgICAvLyDku47popjlubLkuK3mj5Dlj5bnrZTmoYgKICAgIGV4dHJhY3RBbnN3ZXJGcm9tQ29udGVudChjb250ZW50LCBxdWVzdGlvblR5cGUpIHsKICAgICAgLy8g5pSv5oyB55qE5ous5Y+357G75Z6LCiAgICAgIGNvbnN0IGJyYWNrZXRQYXR0ZXJucyA9IFsKICAgICAgICAv44CQKFte44CRXSsp44CRL2csCiAgICAgICAgL1xbKFteXF1dKylcXS9nLAogICAgICAgIC/vvIgoW17vvIldKynvvIkvZywKICAgICAgICAvXCgoW14pXSspXCkvZwogICAgICBdCgogICAgICBmb3IgKGNvbnN0IHBhdHRlcm4gb2YgYnJhY2tldFBhdHRlcm5zKSB7CiAgICAgICAgY29uc3QgbWF0Y2hlcyA9IFsuLi5jb250ZW50Lm1hdGNoQWxsKHBhdHRlcm4pXQogICAgICAgIGlmIChtYXRjaGVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIGNvbnN0IGFuc3dlciA9IG1hdGNoZXNbbWF0Y2hlcy5sZW5ndGggLSAxXVsxXSAvLyDlj5bmnIDlkI7kuIDkuKrljLnphY0KICAgICAgICAgIHJldHVybiB0aGlzLnBhcnNlQW5zd2VyKGFuc3dlciwgcXVlc3Rpb25UeXBlKQogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuICcnCiAgICB9LAoKICAgIC8vIOi9rOaNoumimOWeiwogICAgY29udmVydFF1ZXN0aW9uVHlwZSh0eXBlVGV4dCkgewogICAgICBjb25zdCB0eXBlTWFwID0gewogICAgICAgICfljZXpgInpopgnOiAnc2luZ2xlJywKICAgICAgICAn5aSa6YCJ6aKYJzogJ211bHRpcGxlJywKICAgICAgICAn5Yik5pat6aKYJzogJ2p1ZGdtZW50JwogICAgICB9CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVUZXh0XSB8fCAnc2luZ2xlJwogICAgfSwKCiAgICAvLyDlr7zlhaXmlbDmja4KICAgIGltcG9ydERhdGEoKSB7CiAgICAgIHRoaXMuaW1wb3J0aW5nID0gdHJ1ZQogICAgICBjb25zdCBpbXBvcnREYXRhID0gewogICAgICAgIGJhbmtJZDogdGhpcy5iYW5rSWQsCiAgICAgICAgcXVlc3Rpb25zOiB0aGlzLnBhcnNlZERhdGEKICAgICAgfQogICAgICBiYXRjaEltcG9ydFF1ZXN0aW9ucyhpbXBvcnREYXRhKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmltcG9ydGluZyA9IGZhbHNlCiAgICAgICAgdGhpcy5pbXBvcnRSZXN1bHQgPSByZXNwb25zZS5kYXRhCiAgICAgICAgdGhpcy5uZXh0U3RlcCgpCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICB0aGlzLmltcG9ydGluZyA9IGZhbHNlCiAgICAgICAgY29uc29sZS5lcnJvcign5a+85YWl5pWw5o2u5aSx6LSlJywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5a+85YWl5pWw5o2u5aSx6LSlJykKICAgICAgfSkKICAgIH0sCiAgICAvLyDlrozmiJDlr7zlhaUKICAgIGhhbmRsZUNvbXBsZXRlKCkgewogICAgICB0aGlzLiRlbWl0KCdzdWNjZXNzJykKICAgICAgdGhpcy5oYW5kbGVDbG9zZSgpCiAgICB9LAogICAgLy8g6YeN572u5a+85YWlCiAgICByZXNldEltcG9ydCgpIHsKICAgICAgdGhpcy5jdXJyZW50U3RlcCA9IDAKICAgICAgdGhpcy5pbXBvcnRNb2RlID0gdGhpcy5kZWZhdWx0TW9kZSB8fCAnZG9jdW1lbnQnCiAgICAgIHRoaXMudXBsb2FkZWRGaWxlID0gbnVsbAogICAgICB0aGlzLnBhcnNlZERhdGEgPSBbXQogICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgdGhpcy5pbXBvcnRSZXN1bHQgPSB7CiAgICAgICAgc3VjY2Vzc0NvdW50OiAwLAogICAgICAgIGZhaWxDb3VudDogMCwKICAgICAgICBlcnJvcnM6IFtdCiAgICAgIH0KICAgIH0sCiAgICAvLyDlhbPpl63lr7nor53moYYKICAgIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["BatchImport.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs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file": "BatchImport.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"批量导入题目\"\n    :visible.sync=\"dialogVisible\"\n    width=\"70%\"\n    :before-close=\"handleClose\"\n    append-to-body\n  >\n    <div class=\"import-container\">\n      <!-- 导入步骤 -->\n      <el-steps :active=\"currentStep\" finish-status=\"success\" style=\"margin-bottom: 30px;\">\n        <el-step title=\"下载模板\"></el-step>\n        <el-step title=\"上传文件\"></el-step>\n        <el-step title=\"数据预览\"></el-step>\n        <el-step title=\"导入完成\"></el-step>\n      </el-steps>\n\n      <!-- 步骤1: 选择导入方式 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <div class=\"import-mode-section\">\n          <h3>第一步：文档内容导入</h3>\n\n          <!-- 文档内容导入 -->\n          <div class=\"document-section\">\n            <h4>文档内容导入</h4>\n            <p>支持上传包含题目内容的文档文件，系统将自动解析题目信息</p>\n\n            <div class=\"document-format-tips\">\n              <h4>格式要求：</h4>\n              <div class=\"format-rules\">\n                <div class=\"rule-item\">\n                  <h5>题型标注（必填）：</h5>\n                  <p><code>[单选题]</code> <code>[多选题]</code> <code>[判断题]</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>题号规则（必填）：</h5>\n                  <p>题目前必须有题号，如：<code>1.</code> <code>2：</code> <code>3．</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>选项格式（必填）：</h5>\n                  <p><code>A.选项内容</code> <code>B：选项内容</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>答案标注（必填）：</h5>\n                  <p><code>答案：A</code> 或题干内 <code>【A】</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>解析和难度（可选）：</h5>\n                  <p><code>解析：解析内容</code> <code>难度：中等</code></p>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"document-example\">\n              <h4>示例格式：</h4>\n              <pre class=\"example-text\">\n[单选题]\n1.（ ）是我国最早的诗歌总集。\nA.《左传》\nB.《离骚》\nC.《坛经》\nD.《诗经》\n答案：D\n解析：诗经是我国最早的诗歌总集。\n难度：中等\n\n[判断题]\n2.元杂剧的四大悲剧包括郑光祖的《赵氏孤儿》。\n答案：错误\n解析：《赵氏孤儿》实为纪君祥所作。\n              </pre>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤2: 上传文件 -->\n      <div v-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"upload-section\">\n          <h3>第二步：上传题目文件</h3>\n          <p>请选择包含题目内容的Word文档文件进行上传</p>\n\n          <el-upload\n            ref=\"fileUpload\"\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :on-success=\"handleFileSuccess\"\n            :on-error=\"handleFileError\"\n            :before-upload=\"beforeFileUpload\"\n            :show-file-list=\"false\"\n            accept=\".docx\"\n            drag\n          >\n            <div class=\"upload-area\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"upload-text\">\n                <p>将Word文档拖到此处，或<em>点击上传</em></p>\n                <p class=\"upload-tip\">\n                  支持 .docx 格式文件，文件大小不超过10MB\n                </p>\n              </div>\n            </div>\n          </el-upload>\n\n          <div v-if=\"uploadedFile\" class=\"uploaded-file\">\n            <el-alert\n              :title=\"`已上传文件：${uploadedFile.name}`\"\n              type=\"success\"\n              :closable=\"false\"\n              show-icon\n            />\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" @click=\"parseFile\" :disabled=\"!uploadedFile\" :loading=\"parsing\">\n            解析文件\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 步骤3: 数据预览 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"preview-section\">\n          <h3>第三步：数据预览与确认</h3>\n          <p>共解析到 {{ parsedData.length }} 道题目，请确认数据无误后点击导入</p>\n\n          <div v-if=\"parseErrors.length > 0\" class=\"error-section\">\n            <el-alert\n              title=\"数据解析错误\"\n              type=\"error\"\n              :closable=\"false\"\n              show-icon\n              style=\"margin-bottom: 15px;\"\n            />\n            <div class=\"error-list\">\n              <div v-for=\"(error, index) in parseErrors\" :key=\"index\" class=\"error-item\">\n                第{{ error.row }}行：{{ error.message }}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"preview-table\">\n            <el-table :data=\"parsedData.slice(0, 10)\" border style=\"width: 100%\">\n              <el-table-column prop=\"questionType\" label=\"题型\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-tag :type=\"getQuestionTypeColor(scope.row.questionType)\" size=\"mini\">\n                    {{ getQuestionTypeName(scope.row.questionType) }}\n                  </el-tag>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"questionContent\" label=\"题目内容\" min-width=\"200\" show-overflow-tooltip />\n              <el-table-column prop=\"correctAnswer\" label=\"正确答案\" width=\"100\" />\n              <el-table-column prop=\"difficulty\" label=\"难度\" width=\"80\" />\n            </el-table>\n            <div v-if=\"parsedData.length > 10\" class=\"table-tip\">\n              仅显示前10条数据，共{{ parsedData.length }}条\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button \n            type=\"primary\" \n            @click=\"importData\" \n            :disabled=\"parseErrors.length > 0 || parsedData.length === 0\"\n            :loading=\"importing\"\n          >\n            确认导入\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 步骤4: 导入完成 -->\n      <div v-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"result-section\">\n          <div class=\"result-icon\">\n            <i class=\"el-icon-success\" style=\"font-size: 60px; color: #67c23a;\"></i>\n          </div>\n          <h3>导入完成</h3>\n          <div class=\"result-stats\">\n            <p>成功导入 <span class=\"success-count\">{{ importResult.successCount }}</span> 道题目</p>\n            <p v-if=\"importResult.failCount > 0\">\n              失败 <span class=\"fail-count\">{{ importResult.failCount }}</span> 道题目\n            </p>\n          </div>\n          \n          <div v-if=\"importResult.errors.length > 0\" class=\"import-errors\">\n            <el-collapse>\n              <el-collapse-item title=\"查看失败详情\" name=\"errors\">\n                <div v-for=\"(error, index) in importResult.errors\" :key=\"index\" class=\"error-detail\">\n                  第{{ error.row }}行：{{ error.message }}\n                </div>\n              </el-collapse-item>\n            </el-collapse>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"handleComplete\">完成</el-button>\n          <el-button @click=\"resetImport\">重新导入</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { downloadTemplate, parseImportFile, batchImportQuestions } from '@/api/biz/question'\nimport { download } from '@/utils/request'\n\nexport default {\n  name: \"BatchImport\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    },\n    defaultMode: {\n      type: String,\n      default: 'document' // 只支持document\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      currentStep: 0,\n      importMode: 'document', // 导入模式：只支持document\n      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + getToken()\n      },\n      uploadedFile: null,\n      parsing: false,\n      importing: false,\n      parsedData: [],\n      parseErrors: [],\n      importResult: {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.resetImport()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    },\n    defaultMode(val) {\n      if (val) {\n        this.importMode = val\n      }\n    }\n  },\n  methods: {\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知题型'\n    },\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n    // 下一步\n    nextStep() {\n      this.currentStep++\n    },\n    // 上一步\n    prevStep() {\n      this.currentStep--\n    },\n    // 文件上传前验证\n    beforeFileUpload(file) {\n      const isLt10M = file.size / 1024 / 1024 < 10\n      const isDocx = file.name.toLowerCase().endsWith('.docx')\n\n      if (!isDocx) {\n        this.$message.error('只能上传.docx格式的Word文档!')\n        return false\n      }\n\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!')\n        return false\n      }\n      return true\n    },\n    // 文件上传成功\n    handleFileSuccess(response, file) {\n      if (response.code === 200) {\n        this.uploadedFile = {\n          name: file.name,\n          url: response.url,\n          fileName: response.fileName\n        }\n        this.$message.success('文件上传成功')\n      } else {\n        this.$message.error(response.msg || '文件上传失败')\n      }\n    },\n    // 文件上传失败\n    handleFileError() {\n      this.$message.error('文件上传失败')\n    },\n    // 解析文件\n    parseFile() {\n      this.parsing = true\n      // 文档内容解析\n      this.parseDocumentContent()\n    },\n\n    // 解析文档内容\n    async parseDocumentContent() {\n      try {\n        // 读取文件内容\n        const fileContent = await this.readFileContent(this.uploadedFile.url)\n\n        // 解析题目\n        const parseResult = this.parseQuestionContent(fileContent)\n\n        this.parsing = false\n        this.parsedData = parseResult.questions\n        this.parseErrors = parseResult.errors\n        this.nextStep()\n      } catch (error) {\n        this.parsing = false\n        console.error('解析文档内容失败', error)\n        this.$message.error('解析文档内容失败')\n      }\n    },\n\n    // 读取文件内容\n    readFileContent(fileUrl) {\n      return new Promise((resolve, reject) => {\n        const xhr = new XMLHttpRequest()\n        xhr.open('GET', process.env.VUE_APP_BASE_API + fileUrl, true)\n        xhr.setRequestHeader('Authorization', 'Bearer ' + getToken())\n        xhr.responseType = 'text'\n\n        xhr.onload = function() {\n          if (xhr.status === 200) {\n            resolve(xhr.responseText)\n          } else {\n            reject(new Error('读取文件失败'))\n          }\n        }\n\n        xhr.onerror = function() {\n          reject(new Error('读取文件失败'))\n        }\n\n        xhr.send()\n      })\n    },\n\n    // 解析题目内容\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      try {\n        // 按题型分割内容\n        const sections = this.splitByQuestionType(content)\n\n        sections.forEach((section, sectionIndex) => {\n          try {\n            const parsedQuestions = this.parseSectionQuestions(section)\n            questions.push(...parsedQuestions)\n          } catch (error) {\n            errors.push(`第${sectionIndex + 1}个题型区域解析失败: ${error.message}`)\n          }\n        })\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType, questionIndex) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题号和题干\n      const firstLine = lines[0]\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．]\\s*(.*)/)\n      if (!numberMatch) {\n        throw new Error('题号格式不正确')\n      }\n\n      let questionContent = numberMatch[2]\n      let currentLineIndex = 1\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '中等',\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难)/)\n        if (difficultyMatch) {\n          question.difficulty = difficultyMatch[1]\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 解析答案\n    parseAnswer(answerText, questionType) {\n      if (questionType === 'judgment') {\n        // 判断题答案处理\n        if (answerText.includes('正确') || answerText.includes('对') || answerText.toLowerCase().includes('true')) {\n          return 'true'\n        } else if (answerText.includes('错误') || answerText.includes('错') || answerText.includes('假') || answerText.toLowerCase().includes('false')) {\n          return 'false'\n        }\n        return answerText.trim()\n      } else {\n        // 选择题答案处理\n        return answerText.replace(/[,，\\s]/g, '').toUpperCase()\n      }\n    },\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 导入数据\n    importData() {\n      this.importing = true\n      const importData = {\n        bankId: this.bankId,\n        questions: this.parsedData\n      }\n      batchImportQuestions(importData).then(response => {\n        this.importing = false\n        this.importResult = response.data\n        this.nextStep()\n      }).catch(error => {\n        this.importing = false\n        console.error('导入数据失败', error)\n        this.$message.error('导入数据失败')\n      })\n    },\n    // 完成导入\n    handleComplete() {\n      this.$emit('success')\n      this.handleClose()\n    },\n    // 重置导入\n    resetImport() {\n      this.currentStep = 0\n      this.importMode = this.defaultMode || 'document'\n      this.uploadedFile = null\n      this.parsedData = []\n      this.parseErrors = []\n      this.importResult = {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    },\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.import-container {\n  padding: 20px 0;\n}\n\n.step-content {\n  min-height: 400px;\n}\n\n.template-section h3,\n.upload-section h3,\n.preview-section h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.template-buttons {\n  margin: 20px 0;\n  display: flex;\n  gap: 15px;\n}\n\n.template-tips {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin-top: 20px;\n}\n\n.template-tips h4 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.template-tips ul {\n  margin: 0;\n  padding-left: 20px;\n}\n\n.template-tips li {\n  margin-bottom: 5px;\n  color: #666;\n}\n\n/* 文档导入样式 */\n.import-mode-section h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.document-section {\n  margin-top: 20px;\n}\n\n.document-section h4 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.document-format-tips {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n}\n\n.document-format-tips h4 {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n}\n\n.format-rules {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n}\n\n.rule-item {\n  background: #fff;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 3px solid #409eff;\n}\n\n.rule-item h5 {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.rule-item p {\n  margin: 0;\n  color: #666;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.rule-item code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  color: #e74c3c;\n}\n\n.document-example {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.document-example h4 {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n}\n\n.example-text {\n  background: #fff;\n  padding: 15px;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n.upload-area {\n  text-align: center;\n  padding: 40px 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  background: #fafafa;\n  transition: border-color 0.3s;\n}\n\n.upload-area:hover {\n  border-color: #409EFF;\n}\n\n.upload-area i {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 20px;\n}\n\n.upload-text p {\n  margin: 0;\n  color: #666;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n.uploaded-file {\n  margin-top: 15px;\n}\n\n.error-section {\n  margin-bottom: 20px;\n}\n\n.error-list {\n  max-height: 150px;\n  overflow-y: auto;\n  background: #fef0f0;\n  border: 1px solid #fbc4c4;\n  border-radius: 4px;\n  padding: 10px;\n}\n\n.error-item {\n  color: #f56c6c;\n  font-size: 14px;\n  margin-bottom: 5px;\n}\n\n.table-tip {\n  text-align: center;\n  color: #999;\n  font-size: 12px;\n  margin-top: 10px;\n}\n\n.result-section {\n  text-align: center;\n  padding: 40px 0;\n}\n\n.result-icon {\n  margin-bottom: 20px;\n}\n\n.result-stats {\n  margin: 20px 0;\n}\n\n.success-count {\n  color: #67c23a;\n  font-weight: bold;\n  font-size: 18px;\n}\n\n.fail-count {\n  color: #f56c6c;\n  font-weight: bold;\n  font-size: 18px;\n}\n\n.import-errors {\n  margin-top: 20px;\n  text-align: left;\n}\n\n.error-detail {\n  color: #f56c6c;\n  font-size: 14px;\n  margin-bottom: 5px;\n}\n\n.step-actions {\n  text-align: center;\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #e4e7ed;\n}\n</style>\n"]}]}